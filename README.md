# 跑得快 - iOS扑克牌游戏

一款使用 Swift + SwiftUI 开发的经典扑克牌游戏，支持单机模式，预留联机功能。

## 项目特性

- 🎮 **经典玩法**：三人局，每人16张牌，黑桃3先出
- 🤖 **智能AI**：两个AI对手，支持多种难度
- 📷 **记牌器功能**：拍照识别扑克牌，自动转换为标准格式
- 🎨 **现代UI**：使用SwiftUI构建，支持动画效果
- 📱 **原生体验**：完全原生iOS应用，流畅体验
- 🏗️ **模块化架构**：清晰的代码结构，易于扩展
- 💰 **变现预留**：内购系统接口已预留
- 🌐 **联机预留**：WebSocket联机接口已预留

## 项目结构

```
pdk/
├── Models/                 # 数据模型
│   ├── Card.swift         # 扑克牌模型
│   ├── Player.swift       # 玩家模型
│   └── GameState.swift    # 游戏状态模型
├── GameLogic/             # 游戏逻辑
│   ├── GameEngine.swift   # 游戏引擎
│   ├── RuleValidator.swift # 规则验证器
│   └── AIStrategy.swift   # AI策略
├── UI/                    # 用户界面
│   ├── Views/            # 视图
│   │   ├── MainMenuView.swift    # 主菜单
│   │   ├── GameView.swift        # 游戏界面
│   │   ├── GameResultView.swift  # 结果界面
│   │   └── CardRecorderView.swift # 记牌器界面
│   ├── Components/       # 组件
│   │   ├── CardView.swift        # 卡牌组件
│   │   └── CameraView.swift      # 相机组件
│   └── ViewModels/       # 视图模型
│       └── GameViewModel.swift   # 游戏视图模型
├── Services/              # 服务层
│   ├── DataService.swift         # 数据存储服务
│   ├── InAppPurchaseService.swift # 内购服务
│   └── CardRecognitionService.swift # 扑克牌识别服务
├── Networking/            # 网络模块
│   └── NetworkService.swift      # 网络服务（预留）
└── Assets.xcassets/       # 资源文件
```

## 技术架构

### MVVM架构
- **Model**: 数据模型和业务逻辑
- **View**: SwiftUI视图组件
- **ViewModel**: 连接View和Model的桥梁

### 核心组件

#### 1. 游戏引擎 (GameEngine)
- 游戏流程控制
- 状态管理
- AI回合处理
- 计时器管理

#### 2. 规则验证器 (RuleValidator)
- 出牌规则验证
- 牌型分析
- 游戏提示生成

#### 3. AI策略 (AIStrategy)
- 多种AI策略（激进、保守、平衡、残局）
- 难度调节
- 智能决策算法

#### 4. 数据服务 (DataService)
- 游戏历史记录
- 玩家统计数据
- 成就系统
- 本地数据持久化

#### 5. 扑克牌识别服务 (CardRecognitionService)
- 图像识别功能
- Vision框架集成
- 智能牌型解析
- 识别历史管理

## 游戏规则

### 基本规则
- 三人游戏，每人16张牌
- 拥有黑桃3的玩家先出牌
- 必须出比上家更大的牌型
- 最先出完牌的玩家获胜

### 牌型大小
- **单张**: 3 < 4 < 5 < ... < K < A < 2
- **对子**: 相同点数的两张牌
- **三张**: 相同点数的三张牌
- **顺子**: 五张以上连续的牌
- **连对**: 三对以上连续的对子
- **飞机**: 两个以上连续的三张
- **炸弹**: 四张相同点数的牌，可打任何牌型

### 花色大小
黑桃 > 红桃 > 方块 > 梅花

## 记牌器功能

### 功能特点
- **拍照识别**: 使用相机拍摄扑克牌照片进行自动识别
- **相册选择**: 支持从相册选择包含扑克牌的照片
- **智能解析**: 基于Vision框架的图像识别技术
- **标准格式**: 自动转换为逗号分隔的标准格式
- **历史记录**: 保存识别历史，支持查看和管理
- **统计分析**: 提供识别准确率和使用统计

### 使用方法
1. 在主菜单或游戏界面点击"记牌器"按钮
2. 选择拍照或从相册选择照片
3. 确保扑克牌在照片中清晰可见
4. 等待自动识别完成
5. 查看识别结果和标准格式输出

### 输出格式
- 按牌值大小排序：3 < 4 < 5 < 6 < 7 < 8 < 9 < 10 < J < Q < K < A < 2
- 逗号分隔格式：`3,3,4,5,6,7,8,9,10,J,Q,K,A,2`
- 支持识别多张相同数值的牌
- 自动去重和排序

## 开发指南

### 环境要求
- Xcode 15.0+
- iOS 17.0+
- Swift 5.9+

### 运行项目
1. 克隆项目到本地
2. 使用Xcode打开 `pdk.xcodeproj`
3. 选择目标设备或模拟器
4. 点击运行按钮

### 扩展开发

#### 添加新的AI策略
```swift
// 在AIStrategy.swift中添加新策略
enum AIStrategyType {
    case newStrategy  // 新策略
}

// 实现新策略逻辑
private func makeNewStrategyDecision(_ combinations: [CardCombination], gameState: GameState) -> AIDecision {
    // 实现新的AI决策逻辑
}
```

#### 添加新的牌型
```swift
// 在Card.swift中扩展CardCombinationType
enum CardCombinationType: String, CaseIterable {
    case newCombination = "新牌型"
}

// 在RuleValidator.swift中添加验证逻辑
```

#### 集成联机功能
```swift
// 使用NetworkService.swift中的预留接口
let networkService = NetworkService.shared
networkService.connectToServer()
networkService.createRoom(roomName: "我的房间")
```

## 预留功能

### 1. 联机对战
- WebSocket实时通信
- 房间系统
- 玩家匹配
- 断线重连

### 2. 内购系统
- 金币购买
- 移除广告
- 高级主题
- 特殊道具

### 3. 社交功能
- 好友系统
- 聊天功能
- 战绩分享
- 排行榜

### 4. 游戏增强
- 更多AI难度
- 自定义规则
- 回放系统
- 数据统计

## 配置说明

### 游戏配置
```swift
// 在GameConfig中修改游戏参数
struct GameConfig {
    var playerCount: Int = 3        // 玩家数量
    var cardsPerPlayer: Int = 16    // 每人手牌数
    var timeLimit: Int = 30         // 出牌时间限制
    var enableBomb: Bool = true     // 是否启用炸弹
}
```

### AI难度调节
```swift
// 在AIStrategy.swift中调整AI难度
enum AIDifficulty: String, CaseIterable {
    case easy = "简单"
    case normal = "普通"
    case hard = "困难"
}
```

## 性能优化

- 使用`@Published`属性进行状态管理
- 合理使用`@StateObject`和`@ObservedObject`
- 避免不必要的视图重绘
- 优化动画性能

## 测试建议

### 单元测试
- 游戏规则验证
- AI策略测试
- 数据模型测试

### UI测试
- 用户交互流程
- 动画效果
- 响应式布局

### 集成测试
- 完整游戏流程
- 数据持久化
- 错误处理

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: <EMAIL>
- 项目Issues: [GitHub Issues](https://github.com/your-repo/issues)

---

**注意**: 这是一个完整的iOS游戏项目框架，包含了所有核心功能的实现。联机功能和内购功能已预留接口，可根据需要进行具体实现。
