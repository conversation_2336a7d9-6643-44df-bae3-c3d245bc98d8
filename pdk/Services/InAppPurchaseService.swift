//
//  InAppPurchaseService.swift
//  pdk
//
//  Created by MeeYou on 2025/8/21.
//

import Foundation
import StoreKit

/// 内购服务 - 处理应用内购买逻辑（预留接口）
class InAppPurchaseService: NSObject, ObservableObject {
    
    // MARK: - 单例
    static let shared = InAppPurchaseService()
    
    // MARK: - 发布属性
    @Published var products: [SKProduct] = []
    @Published var purchasedProducts: Set<String> = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // MARK: - 私有属性
    private var productRequest: SKProductsRequest?
    private var completionHandlers: [String: (Bool) -> Void] = [:]
    
    // MARK: - 产品ID
    enum ProductID: String, CaseIterable {
        case coins100 = "com.pdk.coins.100"
        case coins500 = "com.pdk.coins.500"
        case coins1000 = "com.pdk.coins.1000"
        case removeAds = "com.pdk.removeads"
        case premiumThemes = "com.pdk.themes.premium"
        
        var displayName: String {
            switch self {
            case .coins100: return "100金币"
            case .coins500: return "500金币"
            case .coins1000: return "1000金币"
            case .removeAds: return "移除广告"
            case .premiumThemes: return "高级主题包"
            }
        }
        
        var description: String {
            switch self {
            case .coins100: return "购买100个游戏金币"
            case .coins500: return "购买500个游戏金币"
            case .coins1000: return "购买1000个游戏金币"
            case .removeAds: return "永久移除所有广告"
            case .premiumThemes: return "解锁所有高级游戏主题"
            }
        }
        
        var coinValue: Int {
            switch self {
            case .coins100: return 100
            case .coins500: return 500
            case .coins1000: return 1000
            case .removeAds: return 0
            case .premiumThemes: return 0
            }
        }
    }
    
    // MARK: - 初始化
    override init() {
        super.init()
        SKPaymentQueue.default().add(self)
        loadPurchasedProducts()
    }
    
    deinit {
        SKPaymentQueue.default().remove(self)
    }
    
    // MARK: - 公共方法
    
    /// 初始化内购服务
    func initialize() {
        guard SKPaymentQueue.canMakePayments() else {
            setError("设备不支持内购功能")
            return
        }
        
        requestProducts()
    }
    
    /// 请求产品信息
    func requestProducts() {
        isLoading = true
        
        let productIDs = Set(ProductID.allCases.map { $0.rawValue })
        productRequest = SKProductsRequest(productIdentifiers: productIDs)
        productRequest?.delegate = self
        productRequest?.start()
    }
    
    /// 购买产品
    func purchaseProduct(_ productID: ProductID, completion: @escaping (Bool) -> Void) {
        guard let product = products.first(where: { $0.productIdentifier == productID.rawValue }) else {
            completion(false)
            setError("产品不可用")
            return
        }
        
        guard SKPaymentQueue.canMakePayments() else {
            completion(false)
            setError("设备不支持内购功能")
            return
        }
        
        completionHandlers[productID.rawValue] = completion
        
        let payment = SKPayment(product: product)
        SKPaymentQueue.default().add(payment)
    }
    
    /// 恢复购买
    func restorePurchases() {
        isLoading = true
        SKPaymentQueue.default().restoreCompletedTransactions()
    }
    
    /// 检查产品是否已购买
    func isProductPurchased(_ productID: ProductID) -> Bool {
        return purchasedProducts.contains(productID.rawValue)
    }
    
    /// 获取产品价格
    func getProductPrice(_ productID: ProductID) -> String {
        guard let product = products.first(where: { $0.productIdentifier == productID.rawValue }) else {
            return "价格不可用"
        }
        
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.locale = product.priceLocale
        
        return formatter.string(from: product.price) ?? "价格不可用"
    }
    
    // MARK: - 私有方法
    
    private func loadPurchasedProducts() {
        let purchased = UserDefaults.standard.stringArray(forKey: "purchased_products") ?? []
        purchasedProducts = Set(purchased)
    }
    
    private func savePurchasedProducts() {
        UserDefaults.standard.set(Array(purchasedProducts), forKey: "purchased_products")
    }
    
    private func handleSuccessfulPurchase(_ productID: String) {
        purchasedProducts.insert(productID)
        savePurchasedProducts()
        
        // 处理购买后的逻辑
        if let product = ProductID(rawValue: productID) {
            processPurchase(product)
        }
        
        // 调用完成回调
        completionHandlers[productID]?(true)
        completionHandlers.removeValue(forKey: productID)
    }
    
    private func handleFailedPurchase(_ productID: String, error: Error?) {
        if let error = error {
            setError("购买失败: \(error.localizedDescription)")
        } else {
            setError("购买失败")
        }
        
        completionHandlers[productID]?(false)
        completionHandlers.removeValue(forKey: productID)
    }
    
    private func processPurchase(_ product: ProductID) {
        switch product {
        case .coins100, .coins500, .coins1000:
            // 添加金币
            var profile = DataService.shared.getUserProfile()
            profile.coins += product.coinValue
            DataService.shared.saveUserProfile(profile)
            
        case .removeAds:
            // 移除广告标记
            var settings = DataService.shared.getGameSettings()
            // 这里可以添加移除广告的逻辑
            DataService.shared.saveGameSettings(settings)
            
        case .premiumThemes:
            // 解锁高级主题
            var profile = DataService.shared.getUserProfile()
            // 这里可以添加解锁主题的逻辑
            DataService.shared.saveUserProfile(profile)
        }
    }
    
    private func setError(_ message: String) {
        DispatchQueue.main.async {
            self.errorMessage = message
            
            // 3秒后清除错误信息
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                if self.errorMessage == message {
                    self.errorMessage = nil
                }
            }
        }
    }
}

// MARK: - SKProductsRequestDelegate
extension InAppPurchaseService: SKProductsRequestDelegate {
    
    func productsRequest(_ request: SKProductsRequest, didReceive response: SKProductsResponse) {
        DispatchQueue.main.async {
            self.products = response.products
            self.isLoading = false
        }
        
        if !response.invalidProductIdentifiers.isEmpty {
            print("无效的产品ID: \(response.invalidProductIdentifiers)")
        }
    }
    
    func request(_ request: SKRequest, didFailWithError error: Error) {
        DispatchQueue.main.async {
            self.isLoading = false
            self.setError("请求产品信息失败: \(error.localizedDescription)")
        }
    }
}

// MARK: - SKPaymentTransactionObserver
extension InAppPurchaseService: SKPaymentTransactionObserver {
    
    func paymentQueue(_ queue: SKPaymentQueue, updatedTransactions transactions: [SKPaymentTransaction]) {
        for transaction in transactions {
            switch transaction.transactionState {
            case .purchased:
                handleSuccessfulPurchase(transaction.payment.productIdentifier)
                queue.finishTransaction(transaction)
                
            case .failed:
                handleFailedPurchase(transaction.payment.productIdentifier, error: transaction.error)
                queue.finishTransaction(transaction)
                
            case .restored:
                handleSuccessfulPurchase(transaction.payment.productIdentifier)
                queue.finishTransaction(transaction)
                
            case .deferred:
                // 等待外部授权
                break
                
            case .purchasing:
                // 购买中
                break
                
            @unknown default:
                break
            }
        }
        
        DispatchQueue.main.async {
            self.isLoading = false
        }
    }
    
    func paymentQueue(_ queue: SKPaymentQueue, restoreCompletedTransactionsFailedWithError error: Error) {
        DispatchQueue.main.async {
            self.isLoading = false
            self.setError("恢复购买失败: \(error.localizedDescription)")
        }
    }
    
    func paymentQueueRestoreCompletedTransactionsFinished(_ queue: SKPaymentQueue) {
        DispatchQueue.main.async {
            self.isLoading = false
        }
    }
}

// MARK: - 内购商店视图模型
class StoreViewModel: ObservableObject {
    @Published var purchaseService = InAppPurchaseService.shared
    
    init() {
        purchaseService.initialize()
    }
    
    func purchaseCoins(_ productID: InAppPurchaseService.ProductID) {
        purchaseService.purchaseProduct(productID) { success in
            DispatchQueue.main.async {
                if success {
                    // 显示购买成功提示
                    print("购买成功: \(productID.displayName)")
                } else {
                    // 显示购买失败提示
                    print("购买失败: \(productID.displayName)")
                }
            }
        }
    }
    
    func restorePurchases() {
        purchaseService.restorePurchases()
    }
}
