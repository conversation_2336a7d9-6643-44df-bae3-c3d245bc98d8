//
//  DataService.swift
//  pdk
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/21.
//

import Foundation

/// 数据存储服务 - 负责游戏数据的持久化
class DataService: ObservableObject {
    
    // MARK: - 单例
    static let shared = DataService()
    
    // MARK: - 私有属性
    private let userDefaults = UserDefaults.standard
    private let encoder = JSONEncoder()
    private let decoder = JSONDecoder()
    
    // MARK: - 存储键
    private enum StorageKeys {
        static let gameHistory = "game_history"
        static let playerStats = "player_stats"
        static let gameSettings = "game_settings"
        static let userProfile = "user_profile"
        static let achievements = "achievements"
    }
    
    // MARK: - 初始化
    private init() {
        setupEncoder()
    }
    
    private func setupEncoder() {
        encoder.dateEncodingStrategy = .iso8601
        decoder.dateDecodingStrategy = .iso8601
    }
    
    // MARK: - 游戏历史记录
    
    /// 保存游戏结果
    func saveGameResult(_ result: GameResult) {
        var history = getGameHistory()
        history.append(result)
        
        // 只保留最近100场游戏记录
        if history.count > 100 {
            history = Array(history.suffix(100))
        }
        
        saveGameHistory(history)
    }
    
    /// 获取游戏历史记录
    func getGameHistory() -> [GameResult] {
        guard let data = userDefaults.data(forKey: StorageKeys.gameHistory),
              let history = try? decoder.decode([GameResult].self, from: data) else {
            return []
        }
        return history
    }
    
    /// 保存游戏历史记录
    private func saveGameHistory(_ history: [GameResult]) {
        if let data = try? encoder.encode(history) {
            userDefaults.set(data, forKey: StorageKeys.gameHistory)
        }
    }
    
    /// 清除游戏历史记录
    func clearGameHistory() {
        userDefaults.removeObject(forKey: StorageKeys.gameHistory)
    }
    
    // MARK: - 玩家统计
    
    /// 获取玩家统计数据
    func getPlayerStats() -> PlayerStats {
        guard let data = userDefaults.data(forKey: StorageKeys.playerStats),
              let stats = try? decoder.decode(PlayerStats.self, from: data) else {
            return PlayerStats()
        }
        return stats
    }
    
    /// 更新玩家统计数据
    func updatePlayerStats(with result: GameResult, humanPlayerId: UUID) {
        var stats = getPlayerStats()
        
        stats.totalGames += 1
        
        if result.winners.first == humanPlayerId {
            stats.wins += 1
        }
        
        stats.totalScore += result.scores[humanPlayerId] ?? 0
        stats.totalPlayTime += result.duration
        
        // 更新最佳成绩
        if let score = result.scores[humanPlayerId], score > stats.bestScore {
            stats.bestScore = score
        }
        
        // 更新最快胜利时间
        if result.winners.first == humanPlayerId {
            if stats.fastestWin == 0 || result.duration < stats.fastestWin {
                stats.fastestWin = result.duration
            }
        }
        
        stats.lastPlayDate = Date()
        
        savePlayerStats(stats)
    }
    
    /// 保存玩家统计数据
    private func savePlayerStats(_ stats: PlayerStats) {
        if let data = try? encoder.encode(stats) {
            userDefaults.set(data, forKey: StorageKeys.playerStats)
        }
    }
    
    // MARK: - 游戏设置
    
    /// 获取游戏设置
    func getGameSettings() -> GameSettings {
        guard let data = userDefaults.data(forKey: StorageKeys.gameSettings),
              let settings = try? decoder.decode(GameSettings.self, from: data) else {
            return GameSettings()
        }
        return settings
    }
    
    /// 保存游戏设置
    func saveGameSettings(_ settings: GameSettings) {
        if let data = try? encoder.encode(settings) {
            userDefaults.set(data, forKey: StorageKeys.gameSettings)
        }
    }
    
    // MARK: - 用户资料
    
    /// 获取用户资料
    func getUserProfile() -> UserProfile {
        guard let data = userDefaults.data(forKey: StorageKeys.userProfile),
              let profile = try? decoder.decode(UserProfile.self, from: data) else {
            return UserProfile()
        }
        return profile
    }
    
    /// 保存用户资料
    func saveUserProfile(_ profile: UserProfile) {
        if let data = try? encoder.encode(profile) {
            userDefaults.set(data, forKey: StorageKeys.userProfile)
        }
    }
    
    // MARK: - 成就系统
    
    /// 获取成就列表
    func getAchievements() -> [Achievement] {
        guard let data = userDefaults.data(forKey: StorageKeys.achievements),
              let achievements = try? decoder.decode([Achievement].self, from: data) else {
            return Achievement.defaultAchievements
        }
        return achievements
    }
    
    /// 保存成就列表
    func saveAchievements(_ achievements: [Achievement]) {
        if let data = try? encoder.encode(achievements) {
            userDefaults.set(data, forKey: StorageKeys.achievements)
        }
    }
    
    /// 检查并解锁成就
    func checkAchievements(with result: GameResult, humanPlayerId: UUID) {
        var achievements = getAchievements()
        let stats = getPlayerStats()
        
        for i in 0..<achievements.count {
            if !achievements[i].isUnlocked {
                if achievements[i].checkCondition(stats: stats, result: result, humanPlayerId: humanPlayerId) {
                    achievements[i].unlock()
                }
            }
        }
        
        saveAchievements(achievements)
    }
    
    // MARK: - 数据导出/导入
    
    /// 导出所有数据
    func exportAllData() -> [String: Any] {
        return [
            "gameHistory": getGameHistory(),
            "playerStats": getPlayerStats(),
            "gameSettings": getGameSettings(),
            "userProfile": getUserProfile(),
            "achievements": getAchievements(),
            "exportDate": Date()
        ]
    }
    
    /// 导入数据（用于数据恢复）
    func importData(_ data: [String: Any]) -> Bool {
        // 实现数据导入逻辑
        // 这里简化实现，实际应用中需要更严格的验证
        return true
    }
    
    // MARK: - 数据清理
    
    /// 清除所有数据
    func clearAllData() {
        userDefaults.removeObject(forKey: StorageKeys.gameHistory)
        userDefaults.removeObject(forKey: StorageKeys.playerStats)
        userDefaults.removeObject(forKey: StorageKeys.gameSettings)
        userDefaults.removeObject(forKey: StorageKeys.userProfile)
        userDefaults.removeObject(forKey: StorageKeys.achievements)
    }
}

// MARK: - 数据模型

/// 玩家统计数据
struct PlayerStats: Codable {
    var totalGames: Int = 0
    var wins: Int = 0
    var totalScore: Int = 0
    var bestScore: Int = 0
    var totalPlayTime: TimeInterval = 0
    var fastestWin: TimeInterval = 0
    var lastPlayDate: Date?
    
    /// 胜率
    var winRate: Double {
        guard totalGames > 0 else { return 0 }
        return Double(wins) / Double(totalGames)
    }
    
    /// 平均分数
    var averageScore: Double {
        guard totalGames > 0 else { return 0 }
        return Double(totalScore) / Double(totalGames)
    }
}

/// 游戏设置
struct GameSettings: Codable {
    var soundEnabled: Bool = true
    var musicEnabled: Bool = true
    var vibrationEnabled: Bool = true
    var aiDifficulty: AIDifficulty = .normal
    var autoPlay: Bool = false
    var showHints: Bool = true
    var animationSpeed: AnimationSpeed = .normal
    
    enum AnimationSpeed: String, Codable, CaseIterable {
        case slow = "慢"
        case normal = "正常"
        case fast = "快"
    }
}

/// 用户资料
struct UserProfile: Codable {
    var nickname: String = "玩家"
    var avatar: String = "default"
    var coins: Int = 1000
    var level: Int = 1
    var experience: Int = 0
    var createDate: Date = Date()
    
    /// 升级所需经验
    var experienceToNextLevel: Int {
        return level * 100 - experience
    }
}

/// 成就
struct Achievement: Codable, Identifiable {
    let id: UUID = UUID()
    let title: String
    let description: String
    let icon: String
    let condition: AchievementCondition
    var isUnlocked: Bool = false
    var unlockedDate: Date?

    /// CodingKeys 用于 Codable 协议，排除 id 属性
    enum CodingKeys: String, CodingKey {
        case title, description, icon, condition, isUnlocked, unlockedDate
    }
    
    /// 检查成就条件
    func checkCondition(stats: PlayerStats, result: GameResult, humanPlayerId: UUID) -> Bool {
        switch condition {
        case .firstWin:
            return result.winners.first == humanPlayerId && stats.wins == 1
        case .winStreak(let count):
            // 简化实现，实际需要检查连胜记录
            return stats.wins >= count
        case .totalGames(let count):
            return stats.totalGames >= count
        case .highScore(let score):
            return result.scores[humanPlayerId] ?? 0 >= score
        case .fastWin(let time):
            return result.winners.first == humanPlayerId && result.duration <= time
        }
    }
    
    /// 解锁成就
    mutating func unlock() {
        isUnlocked = true
        unlockedDate = Date()
    }
    
    /// 默认成就列表
    static let defaultAchievements: [Achievement] = [
        Achievement(title: "初出茅庐", description: "赢得第一场游戏", icon: "star.fill", condition: .firstWin),
        Achievement(title: "游戏达人", description: "完成10场游戏", icon: "gamecontroller.fill", condition: .totalGames(10)),
        Achievement(title: "高分选手", description: "单局得分超过200", icon: "trophy.fill", condition: .highScore(200)),
        Achievement(title: "速战速决", description: "在2分钟内获胜", icon: "timer", condition: .fastWin(120)),
        Achievement(title: "连胜王者", description: "连续获胜5场", icon: "crown.fill", condition: .winStreak(5))
    ]
}

/// 成就条件
enum AchievementCondition: Codable {
    case firstWin
    case winStreak(Int)
    case totalGames(Int)
    case highScore(Int)
    case fastWin(TimeInterval)
}
