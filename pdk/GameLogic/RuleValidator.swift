//
//  RuleValidator.swift
//  pdk
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/21.
//

import Foundation

/// 跑得快游戏规则验证器
class RuleValidator {
    
    /// 验证出牌是否有效
    func validatePlay(_ combination: CardCombination, gameState: GameState) -> <PERSON><PERSON> {
        guard let currentPlayer = gameState.currentPlayer else { return false }
        
        // 1. 检查牌型是否有效
        if !combination.isValid {
            return false
        }
        
        // 2. 检查玩家是否拥有这些牌
        if !currentPlayer.canPlay(combination) {
            return false
        }
        
        // 3. 检查首轮出牌规则（必须包含黑桃3）
        if isFirstRound(gameState) {
            return validateFirstRound(combination, player: currentPlayer)
        }
        
        // 4. 检查是否可以打过当前牌型
        if let currentCombination = gameState.currentCombination {
            return CardCombination.canBeat(combination, currentCombination)
        }
        
        // 5. 如果没有当前牌型，任何有效牌型都可以出
        return true
    }
    
    /// 验证是否可以过牌
    func canPass(gameState: GameState) -> Bool {
        // 首轮不能过牌（必须出黑桃3）
        if isFirstRound(gameState) {
            return false
        }
        
        // 如果没有当前牌型，不能过牌
        if gameState.currentCombination == nil {
            return false
        }
        
        return true
    }
    
    /// 获取玩家可出的有效牌型
    func getValidCombinations(for player: Player, gameState: GameState) -> [CardCombination] {
        let allCombinations = player.getPossibleCombinations()
        
        return allCombinations.filter { combination in
            validatePlay(combination, gameState: gameState)
        }
    }
    
    /// 检查是否为首轮
    private func isFirstRound(_ gameState: GameState) -> Bool {
        return gameState.gameHistory.isEmpty
    }
    
    /// 验证首轮出牌（必须包含黑桃3）
    private func validateFirstRound(_ combination: CardCombination, player: Player) -> Bool {
        // 首轮必须包含黑桃3
        return combination.cards.contains { $0.isSpadeThree }
    }
    
    /// 分析牌型详细信息
    func analyzeCombination(_ cards: [Card]) -> CardCombinationAnalysis {
        let combination = CardCombination(cards: cards)
        
        return CardCombinationAnalysis(
            combination: combination,
            isValid: combination.isValid,
            strength: calculateStrength(combination),
            suggestions: generateSuggestions(cards)
        )
    }
    
    /// 计算牌型强度
    private func calculateStrength(_ combination: CardCombination) -> Int {
        guard combination.isValid else { return 0 }
        
        let baseStrength = combination.type.weight * 100
        let rankStrength = combination.mainRank?.weight ?? 0
        
        return baseStrength + rankStrength
    }
    
    /// 生成出牌建议
    private func generateSuggestions(_ cards: [Card]) -> [String] {
        var suggestions: [String] = []
        
        if cards.isEmpty {
            suggestions.append("请选择要出的牌")
            return suggestions
        }
        
        let combination = CardCombination(cards: cards)
        
        if !combination.isValid {
            suggestions.append("当前选择的牌不构成有效牌型")
            
            // 根据牌数给出建议
            switch cards.count {
            case 1:
                suggestions.append("单张牌可以直接出")
            case 2:
                if !isPair(cards) {
                    suggestions.append("两张牌需要是相同点数才能组成对子")
                }
            case 3:
                if !isTriple(cards) {
                    suggestions.append("三张牌需要是相同点数才能组成三张")
                }
            case 4:
                if !isBomb(cards) {
                    suggestions.append("四张牌需要是相同点数才能组成炸弹")
                }
            default:
                if cards.count >= 5 {
                    suggestions.append("五张以上的牌可以尝试组成顺子、连对或飞机")
                }
            }
        } else {
            suggestions.append("这是一个有效的\(combination.type.rawValue)")
        }
        
        return suggestions
    }
    
    /// 检查是否为对子
    private func isPair(_ cards: [Card]) -> Bool {
        guard cards.count == 2 else { return false }
        return cards[0].rank == cards[1].rank
    }
    
    /// 检查是否为三张
    private func isTriple(_ cards: [Card]) -> Bool {
        guard cards.count == 3 else { return false }
        let ranks = cards.map { $0.rank }
        return ranks.allSatisfy { $0 == ranks.first }
    }
    
    /// 检查是否为炸弹
    private func isBomb(_ cards: [Card]) -> Bool {
        guard cards.count == 4 else { return false }
        let ranks = cards.map { $0.rank }
        return ranks.allSatisfy { $0 == ranks.first }
    }
    
    /// 检查是否为顺子
    func isStraight(_ cards: [Card]) -> Bool {
        guard cards.count >= 5 else { return false }
        
        let sortedRanks = cards.map { $0.rank }.sorted { $0.weight < $1.weight }
        
        // 检查是否有重复
        let uniqueRanks = Set(sortedRanks)
        if uniqueRanks.count != sortedRanks.count {
            return false
        }
        
        // 检查是否连续
        for i in 1..<sortedRanks.count {
            if sortedRanks[i].weight != sortedRanks[i-1].weight + 1 {
                return false
            }
        }
        
        return true
    }
    
    /// 检查是否为连对
    func isPairStraight(_ cards: [Card]) -> Bool {
        guard cards.count >= 6 && cards.count % 2 == 0 else { return false }
        
        let rankGroups = Dictionary(grouping: cards, by: { $0.rank })
        
        // 每个点数必须恰好有2张
        if !rankGroups.values.allSatisfy({ $0.count == 2 }) {
            return false
        }
        
        let sortedRanks = rankGroups.keys.sorted { $0.weight < $1.weight }
        
        // 检查是否连续
        for i in 1..<sortedRanks.count {
            if sortedRanks[i].weight != sortedRanks[i-1].weight + 1 {
                return false
            }
        }
        
        return true
    }
    
    /// 检查是否为飞机
    func isTripleStraight(_ cards: [Card]) -> Bool {
        guard cards.count >= 6 && cards.count % 3 == 0 else { return false }
        
        let rankGroups = Dictionary(grouping: cards, by: { $0.rank })
        
        // 每个点数必须恰好有3张
        if !rankGroups.values.allSatisfy({ $0.count == 3 }) {
            return false
        }
        
        let sortedRanks = rankGroups.keys.sorted { $0.weight < $1.weight }
        
        // 检查是否连续
        for i in 1..<sortedRanks.count {
            if sortedRanks[i].weight != sortedRanks[i-1].weight + 1 {
                return false
            }
        }
        
        return true
    }
    
    /// 获取牌型提示
    func getPlayHints(for player: Player, gameState: GameState) -> [PlayHint] {
        var hints: [PlayHint] = []
        
        let validCombinations = getValidCombinations(for: player, gameState: gameState)
        
        if validCombinations.isEmpty {
            if canPass(gameState: gameState) {
                hints.append(PlayHint(type: .pass, description: "当前无法出牌，建议过牌"))
            } else {
                hints.append(PlayHint(type: .mustPlay, description: "必须出牌"))
            }
        } else {
            // 按牌型强度排序，推荐最优出牌
            let sortedCombinations = validCombinations.sorted { 
                calculateStrength($0) < calculateStrength($1) 
            }
            
            if let bestCombination = sortedCombinations.first {
                hints.append(PlayHint(
                    type: .recommend,
                    description: "推荐出\(bestCombination.type.rawValue)",
                    combination: bestCombination
                ))
            }
            
            // 如果有炸弹，特别提示
            let bombs = validCombinations.filter { $0.type == .bomb }
            if !bombs.isEmpty {
                hints.append(PlayHint(
                    type: .bomb,
                    description: "您有炸弹可以出",
                    combination: bombs.first
                ))
            }
        }
        
        return hints
    }
}

// MARK: - 辅助结构体

/// 牌型分析结果
struct CardCombinationAnalysis {
    let combination: CardCombination
    let isValid: Bool
    let strength: Int
    let suggestions: [String]
}

/// 出牌提示类型
enum PlayHintType {
    case pass           // 建议过牌
    case mustPlay       // 必须出牌
    case recommend      // 推荐出牌
    case bomb           // 炸弹提示
}

/// 出牌提示
struct PlayHint {
    let type: PlayHintType
    let description: String
    let combination: CardCombination?
    
    init(type: PlayHintType, description: String, combination: CardCombination? = nil) {
        self.type = type
        self.description = description
        self.combination = combination
    }
}
