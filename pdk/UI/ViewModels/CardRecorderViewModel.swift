//
//  CardRecorderViewModel.swift
//  pdk
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/22.
//

import SwiftUI
import Combine

/// 记牌器视图模型
class CardRecorderViewModel: ObservableObject {
    
    // MARK: - 发布属性
    @Published var myHandCards: [Card] = []
    @Published var opponentPlays: [OpponentPlay] = []
    @Published var aiAdvice: AIAdvice?
    @Published var isAnalyzing = false
    @Published var errorMessage: String?
    
    // MARK: - 游戏状态
    @Published var currentGameState: RecorderGameState = .waitingForHandCards
    @Published var remainingCards: [Card: Int] = [:]
    
    // MARK: - 私有属性
    private var cancellables = Set<AnyCancellable>()
    private let recognitionService = CardRecognitionService.shared
    
    // MARK: - 初始化
    init() {
        setupBindings()
        initializeRemainingCards()
    }
    
    // MARK: - 设置绑定
    private func setupBindings() {
        // 监听手牌变化，自动更新剩余牌数
        $myHandCards
            .sink { [weak self] handCards in
                self?.updateRemainingCards()
                self?.updateGameState()
            }
            .store(in: &cancellables)
        
        // 监听对手出牌变化，自动更新剩余牌数和AI建议
        $opponentPlays
            .sink { [weak self] _ in
                self?.updateRemainingCards()
                self?.requestAIAdvice()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 手牌管理
    
    /// 识别手牌
    func recognizeHandCards(from image: UIImage) {
        isAnalyzing = true
        errorMessage = nil

        recognitionService.recognizeCards(from: image) { [weak self] result in
            DispatchQueue.main.async {
                self?.isAnalyzing = false

                switch result {
                case .success(let recognitionResult):
                    // 将识别结果转换为Card对象
                    let cards = self?.convertRecognitionResultToCards(recognitionResult) ?? []
                    self?.setHandCards(cards) // 使用新方法设置手牌，会自动更新牌库
                    self?.clearError()
                case .failure(let error):
                    self?.setError("手牌识别失败: \(error.localizedDescription)")
                }
            }
        }
    }

    /// 设置手牌并更新牌库
    private func setHandCards(_ cards: [Card]) {
        myHandCards = cards
        // 手牌变化会通过 setupBindings 中的监听自动触发 updateRemainingCards()
        // 这样就会从牌库中移除已识别的手牌
    }
    
    /// 手动添加手牌
    func addHandCard(_ card: Card) {
        // 检查牌库中是否还有这张牌
        if let remainingCount = remainingCards[card], remainingCount > 0 {
            if !myHandCards.contains(card) {
                myHandCards.append(card)
                myHandCards.sort { $0.rank.weight < $1.rank.weight }
                // 手牌变化会通过监听自动更新牌库
            }
        } else {
            setError("这张牌已经被使用或不存在于牌库中")
        }
    }
    
    /// 移除手牌
    func removeHandCard(_ card: Card) {
        myHandCards.removeAll { $0.id == card.id }
    }
    
    /// 清空手牌
    func clearHandCards() {
        myHandCards.removeAll()
    }
    
    // MARK: - 对手出牌管理
    
    /// 添加对手出牌记录
    func addOpponentPlay(player: OpponentPlayer, cards: [Card]) {
        // 检查要添加的牌是否都在牌库中
        var unavailableCards: [Card] = []
        for card in cards {
            if let remainingCount = remainingCards[card], remainingCount <= 0 {
                unavailableCards.append(card)
            }
        }

        if !unavailableCards.isEmpty {
            let cardNames = unavailableCards.map { "\($0.rank.displayName)\($0.suit.rawValue)" }.joined(separator: ", ")
            setError("以下牌已经被使用或不存在于牌库中: \(cardNames)")
            return
        }

        let play = OpponentPlay(
            player: player,
            cards: cards,
            timestamp: Date()
        )
        opponentPlays.append(play)
        // 对手出牌变化会通过监听自动更新牌库
    }
    
    /// 移除对手出牌记录
    func removeOpponentPlay(at index: Int) {
        guard index < opponentPlays.count else { return }
        opponentPlays.remove(at: index)
    }
    
    /// 清空对手出牌记录
    func clearOpponentPlays() {
        opponentPlays.removeAll()
    }
    
    // MARK: - AI决策
    
    /// 请求AI建议
    func requestAIAdvice() {
        guard !myHandCards.isEmpty else {
            aiAdvice = nil
            return
        }
        
        isAnalyzing = true
        
        // 模拟AI分析过程
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.generateAIAdvice()
            self?.isAnalyzing = false
        }
    }
    
    /// 生成AI建议（后续会连接后端API）
    private func generateAIAdvice() {
        // 这里后续会调用后端AI接口
        // 目前使用简单的本地逻辑
        
        let currentCombination = opponentPlays.last?.combination
        let validCombinations = getValidCombinations(currentCombination: currentCombination)
        
        if let bestCombination = selectBestCombination(from: validCombinations) {
            aiAdvice = AIAdvice(
                recommendedPlay: bestCombination,
                confidence: 0.85,
                reasoning: generateReasoning(for: bestCombination),
                alternatives: Array(validCombinations.prefix(3))
            )
        } else {
            aiAdvice = AIAdvice(
                recommendedPlay: nil,
                confidence: 0.9,
                reasoning: "建议过牌，当前手牌无法打过对手",
                alternatives: []
            )
        }
    }
    
    // MARK: - 辅助方法

    /// 将识别结果转换为Card对象数组
    private func convertRecognitionResultToCards(_ result: CardRecognitionResult) -> [Card] {
        var cards: [Card] = []

        // 默认花色分配策略：循环分配花色
        let suits = CardSuit.allCases

        for (index, cardString) in result.rawCards.enumerated() {
            if let rank = parseCardRank(from: cardString) {
                let suit = suits[index % suits.count] // 循环分配花色
                let card = Card(suit: suit, rank: rank)
                cards.append(card)
            }
        }

        return cards.sorted()
    }

    /// 解析字符串为CardRank（跑得快48张牌：去掉3张2和1张A）
    private func parseCardRank(from string: String) -> CardRank? {
        switch string.uppercased() {
        case "3": return .three
        case "4": return .four
        case "5": return .five
        case "6": return .six
        case "7": return .seven
        case "8": return .eight
        case "9": return .nine
        case "10": return .ten
        case "J": return .jack
        case "Q": return .queen
        case "K": return .king
        case "A": return .ace
        case "2": return .two
        default: return nil
        }
    }

    private func initializeRemainingCards() {
        // 初始化跑得快牌库（48张：去掉3张2和1张A）
        remainingCards.removeAll()

        for suit in CardSuit.allCases {
            for rank in CardRank.allCases {
                // 跑得快规则：去掉3张2（保留1张黑桃2）和1张A（去掉黑桃A）
                if rank == .two && suit != .spades {
                    continue // 跳过红桃2、方块2、梅花2
                }
                if rank == .ace && suit == .spades {
                    continue // 跳过黑桃A
                }

                let card = Card(suit: suit, rank: rank)
                remainingCards[card] = 1
            }
        }
    }
    
    private func updateRemainingCards() {
        initializeRemainingCards()

        // 减去我的手牌
        for card in myHandCards {
            remainingCards[card] = 0
        }

        // 减去对手已出的牌
        for play in opponentPlays {
            for card in play.cards {
                remainingCards[card] = 0
            }
        }
    }

    /// 获取剩余牌数统计
    func getRemainingCardsCount() -> Int {
        return remainingCards.values.reduce(0, +)
    }

    /// 获取特定牌的剩余数量
    func getRemainingCount(for card: Card) -> Int {
        return remainingCards[card] ?? 0
    }

    /// 获取剩余的牌列表
    func getRemainingCardsList() -> [Card] {
        return remainingCards.compactMap { card, count in
            count > 0 ? card : nil
        }.sorted()
    }

    /// 获取跑得快牌库说明
    func getDeckDescription() -> String {
        return "跑得快48张牌：去掉3张2（保留♠2）和1张A（去掉♠A）"
    }

    /// 检查牌是否在跑得快牌库中
    func isCardInDeck(_ card: Card) -> Bool {
        // 跑得快规则：去掉3张2（保留1张黑桃2）和1张A（去掉黑桃A）
        if card.rank == .two && card.suit != .spades {
            return false // 红桃2、方块2、梅花2不在牌库中
        }
        if card.rank == .ace && card.suit == .spades {
            return false // 黑桃A不在牌库中
        }
        return true
    }
    
    private func updateGameState() {
        if myHandCards.isEmpty {
            currentGameState = .waitingForHandCards
        } else if opponentPlays.isEmpty {
            currentGameState = .waitingForOpponentPlay
        } else {
            currentGameState = .analyzing
        }
    }
    
    private func getValidCombinations(currentCombination: CardCombination?) -> [CardCombination] {
        // 简化的有效组合获取逻辑
        // 后续会使用更复杂的算法
        return []
    }
    
    private func selectBestCombination(from combinations: [CardCombination]) -> CardCombination? {
        // 简化的最佳组合选择逻辑
        return combinations.first
    }
    
    private func generateReasoning(for combination: CardCombination) -> String {
        return "基于当前局势分析，这是最优的出牌选择"
    }
    
    private func setError(_ message: String) {
        errorMessage = message
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) { [weak self] in
            self?.clearError()
        }
    }
    
    private func clearError() {
        errorMessage = nil
    }
}

// MARK: - 数据模型

/// 对手玩家
enum OpponentPlayer: String, CaseIterable {
    case leftPlayer = "上家"
    case rightPlayer = "下家"
}

/// 对手出牌记录
struct OpponentPlay: Identifiable {
    let id = UUID()
    let player: OpponentPlayer
    let cards: [Card]
    let timestamp: Date
    
    var combination: CardCombination {
        return CardCombination(cards: cards)
    }
}

/// AI建议
struct AIAdvice {
    let recommendedPlay: CardCombination?
    let confidence: Double
    let reasoning: String
    let alternatives: [CardCombination]
}

/// 记牌器游戏状态
enum RecorderGameState {
    case waitingForHandCards
    case waitingForOpponentPlay
    case analyzing
}
