//
//  GameViewModel.swift
//  pdk
//
//  Created by MeeY<PERSON> on 2025/8/21.
//

import Foundation
import SwiftUI
import Combine

/// 游戏视图模型 - MVVM架构的核心
class GameViewModel: ObservableObject {
    
    // MARK: - 发布属性
    @Published var gameEngine: GameEngine
    @Published var selectedCards: Set<Card> = []
    @Published var showGameResult: Bool = false
    @Published var showSettings: Bool = false
    @Published var showRules: Bool = false
    @Published var isLoading: Bool = false
    @Published var animationState: AnimationState = .idle
    
    // MARK: - 计算属性
    var gameState: GameState {
        gameEngine.gameState
    }
    
    var currentPlayer: Player? {
        gameState.currentPlayer
    }
    
    var humanPlayer: Player? {
        gameState.players.first { $0.type == .human }
    }
    
    var isHumanTurn: Bool {
        currentPlayer?.type == .human
    }
    
    var canPlaySelectedCards: Bool {
        !selectedCards.isEmpty && gameEngine.canPlayCards(Array(selectedCards))
    }
    
    var canPass: Bool {
        gameState.currentCombination != nil && isHumanTurn
    }
    
    // MARK: - 私有属性
    private var cancellables = Set<AnyCancellable>()
    private let hapticFeedback = UIImpactFeedbackGenerator(style: .medium)
    
    // MARK: - 初始化
    init(gameMode: GameMode = .single, config: GameConfig = .default) {
        self.gameEngine = GameEngine(gameMode: gameMode, config: config)
        setupBindings()
    }
    
    // MARK: - 设置绑定
    private func setupBindings() {
        // 监听游戏状态变化
        gameEngine.$gameState
            .sink { [weak self] gameState in
                self?.handleGameStateChange(gameState)
            }
            .store(in: &cancellables)
        
        // 监听游戏引擎错误
        gameEngine.$errorMessage
            .compactMap { $0 }
            .sink { [weak self] error in
                self?.handleError(error)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 游戏控制
    
    /// 开始新游戏
    func startNewGame() {
        isLoading = true
        selectedCards.removeAll()
        showGameResult = false
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            self?.gameEngine.createNewGame()
            self?.gameEngine.startGame()
            self?.isLoading = false
            self?.animationState = .dealing
        }
    }
    
    /// 玩家出牌
    func playSelectedCards() {
        guard !selectedCards.isEmpty else { return }
        
        let cards = Array(selectedCards)
        animationState = .playing
        
        if gameEngine.playCards(cards) {
            selectedCards.removeAll()
            hapticFeedback.impactOccurred()
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            self?.animationState = .idle
        }
    }
    
    /// 玩家过牌
    func passCards() {
        animationState = .passing
        
        if gameEngine.passCards() {
            hapticFeedback.impactOccurred()
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
            self?.animationState = .idle
        }
    }
    
    /// 切换卡牌选择状态
    func toggleCardSelection(_ card: Card) {
        if selectedCards.contains(card) {
            selectedCards.remove(card)
        } else {
            selectedCards.insert(card)
        }

        // 轻微震动反馈
        let lightFeedback = UIImpactFeedbackGenerator(style: .light)
        lightFeedback.impactOccurred()
    }

    /// 双击出牌 - 当卡牌已选中时双击直接出牌
    func doubleTapToPlay(_ card: Card) {
        // 确保卡牌已选中且是人类玩家的回合
        guard selectedCards.contains(card), isHumanTurn else { return }

        // 如果只选中了一张牌，直接出这张牌
        if selectedCards.count == 1 {
            playSelectedCards()
        } else {
            // 如果选中了多张牌，检查是否可以出牌
            if canPlaySelectedCards {
                playSelectedCards()
            }
        }
    }
    
    /// 清除选择
    func clearSelection() {
        selectedCards.removeAll()
    }
    
    /// 自动选择建议牌型
    func selectRecommendedCards() {
        guard let player = humanPlayer else { return }
        
        let validCombinations = gameEngine.getValidCombinations()
        
        if let recommended = validCombinations.first {
            selectedCards = Set(recommended.cards)
        }
    }
    
    // MARK: - 游戏状态处理
    
    private func handleGameStateChange(_ gameState: GameState) {
        switch gameState.phase {
        case .dealing:
            animationState = .dealing
        case .playing:
            if animationState == .dealing {
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
                    self?.animationState = .idle
                }
            }
        case .finished:
            showGameResult = true
            animationState = .gameOver
        default:
            break
        }
    }
    
    private func handleError(_ error: String) {
        // 显示错误提示
        print("游戏错误: \(error)")
        
        // 可以在这里添加错误提示UI
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            let errorFeedback = UINotificationFeedbackGenerator()
            errorFeedback.notificationOccurred(.error)
        }
    }
    
    // MARK: - UI辅助方法
    
    /// 获取玩家手牌（用于UI显示）
    func getPlayerCards(for player: Player) -> [Card] {
        return player.cards
    }
    
    /// 获取玩家显示名称
    func getPlayerDisplayName(for player: Player) -> String {
        if player.type == .human {
            return "您"
        }
        return player.name
    }
    
    /// 获取玩家状态描述
    func getPlayerStatusDescription(for player: Player) -> String {
        switch player.status {
        case .waiting:
            return "等待中"
        case .playing:
            if player.id == currentPlayer?.id {
                return "出牌中"
            }
            return "游戏中"
        case .finished:
            return "已完成"
        case .offline:
            return "离线"
        }
    }
    
    /// 获取当前牌型描述
    func getCurrentCombinationDescription() -> String {
        guard let combination = gameState.currentCombination else {
            return "自由出牌"
        }
        
        return "\(combination.type.rawValue) - \(combination.cards.map { $0.displayName }.joined(separator: " "))"
    }
    
    /// 获取游戏时间描述
    func getGameTimeDescription() -> String {
        guard let startTime = gameState.startTime else {
            return "00:00"
        }
        
        let duration = Date().timeIntervalSince(startTime)
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    /// 获取剩余时间描述
    func getRemainingTimeDescription() -> String {
        let remaining = gameState.remainingTime
        return String(format: "%02d", max(0, remaining))
    }
    
    /// 检查卡牌是否被选中
    func isCardSelected(_ card: Card) -> Bool {
        return selectedCards.contains(card)
    }
    
    /// 获取出牌提示
    func getPlayHints() -> [String] {
        guard let player = humanPlayer else { return [] }
        
        let ruleValidator = RuleValidator()
        let hints = ruleValidator.getPlayHints(for: player, gameState: gameState)
        
        return hints.map { $0.description }
    }
    
    /// 获取游戏结果描述
    func getGameResultDescription() -> String {
        guard let result = gameState.result else {
            return "游戏进行中"
        }
        
        if let humanPlayerId = humanPlayer?.id,
           result.winners.first == humanPlayerId {
            return "恭喜您获得胜利！"
        } else {
            return "游戏结束，再接再厉！"
        }
    }
    
    // MARK: - 设置和配置
    
    /// 显示设置界面
    func showSettingsView() {
        showSettings = true
    }
    
    /// 显示规则界面
    func showRulesView() {
        showRules = true
    }
    
    /// 更新游戏配置
    func updateGameConfig(_ config: GameConfig) {
        // 如果游戏正在进行，需要重新开始
        if gameState.phase == .playing {
            // 提示用户是否重新开始游戏
            startNewGame()
        }
    }
    
    // MARK: - 调试和统计
    
    /// 获取游戏统计信息
    func getGameStats() -> [String: Any] {
        return gameEngine.getGameStats()
    }
    
    /// 获取调试信息
    func getDebugInfo() -> String {
        var info = "=== 游戏调试信息 ===\n"
        info += "游戏ID: \(gameState.gameId)\n"
        info += "当前阶段: \(gameState.phase.rawValue)\n"
        info += "当前玩家: \(currentPlayer?.name ?? "无")\n"
        info += "回合数: \(gameState.roundNumber)\n"
        info += "选中卡牌: \(selectedCards.count)张\n"
        info += "动画状态: \(animationState)\n"
        
        return info
    }
}

// MARK: - 动画状态枚举
enum AnimationState {
    case idle       // 空闲
    case dealing    // 发牌中
    case playing    // 出牌中
    case passing    // 过牌中
    case gameOver   // 游戏结束
}

// MARK: - 扩展：便利方法
extension GameViewModel {
    
    /// 快速重新开始游戏
    func quickRestart() {
        startNewGame()
    }
    
    /// 获取当前可出的牌型数量
    var availableCombinationsCount: Int {
        return gameEngine.getValidCombinations().count
    }
    
    /// 检查是否为游戏开始
    var isGameStarted: Bool {
        return gameState.phase == .playing || gameState.phase == .finished
    }
    
    /// 检查是否可以开始游戏
    var canStartGame: Bool {
        return gameState.phase == .ready
    }
}
