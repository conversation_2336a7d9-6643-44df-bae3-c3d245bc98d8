//
//  MainMenuView.swift
//  pdk
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/21.
//

import SwiftUI

/// 主菜单界面 - 游戏大厅
struct MainMenuView: View {
    @StateObject private var gameViewModel = GameViewModel()
    @State private var showGameView = false
    @State private var showSettings = false
    @State private var showRules = false
    @State private var showAbout = false
    @State private var showCardRecorder = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景渐变
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.blue.opacity(0.8),
                        Color.purple.opacity(0.6)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: 30) {
                    Spacer()
                    
                    // 游戏标题
                    VStack(spacing: 10) {
                        Text("跑得快")
                            .font(.system(size: 48, weight: .bold, design: .rounded))
                            .foregroundColor(.white)
                            .shadow(color: .black.opacity(0.3), radius: 5, x: 2, y: 2)
                        
                        Text("经典扑克牌游戏")
                            .font(.title3)
                            .foregroundColor(.white.opacity(0.8))
                    }
                    
                    Spacer()
                    
                    // 主要按钮组
                    VStack(spacing: 20) {
                        // 开始游戏按钮
                        MenuButton(
                            title: "开始游戏",
                            icon: "play.circle.fill",
                            color: .green
                        ) {
                            startSinglePlayerGame()
                        }
                        
                        // 联机游戏按钮（预留）
                        MenuButton(
                            title: "联机游戏",
                            icon: "wifi",
                            color: .blue,
                            isDisabled: true
                        ) {
                            // 预留联机功能
                        }
                        
                        // 记牌器按钮
                        MenuButton(
                            title: "记牌器",
                            icon: "camera.viewfinder",
                            color: .purple
                        ) {
                            showCardRecorder = true
                        }

                        // 排行榜按钮（预留）
                        MenuButton(
                            title: "排行榜",
                            icon: "trophy.fill",
                            color: .orange,
                            isDisabled: true
                        ) {
                            // 预留排行榜功能
                        }
                    }
                    
                    Spacer()
                    
                    // 底部按钮组
                    HStack(spacing: 30) {
                        // 游戏规则
                        BottomButton(icon: "book.fill", title: "规则") {
                            showRules = true
                        }
                        
                        // 设置
                        BottomButton(icon: "gearshape.fill", title: "设置") {
                            showSettings = true
                        }
                        
                        // 关于
                        BottomButton(icon: "info.circle.fill", title: "关于") {
                            showAbout = true
                        }
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 40)
            }
        }
        .navigationBarHidden(true)
        .fullScreenCover(isPresented: $showGameView) {
            GameView(viewModel: gameViewModel)
        }
        .sheet(isPresented: $showSettings) {
            SettingsView()
        }
        .sheet(isPresented: $showRules) {
            RulesView()
        }
        .sheet(isPresented: $showAbout) {
            AboutView()
        }
        .fullScreenCover(isPresented: $showCardRecorder) {
            CardRecorderView()
        }
    }
    
    // MARK: - 私有方法
    
    private func startSinglePlayerGame() {
        gameViewModel.startNewGame()
        showGameView = true
    }
}

// MARK: - 菜单按钮组件
struct MenuButton: View {
    let title: String
    let icon: String
    let color: Color
    var isDisabled: Bool = false
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 15) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.white)
                
                Text(title)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Spacer()
            }
            .padding(.horizontal, 25)
            .padding(.vertical, 18)
            .background(
                RoundedRectangle(cornerRadius: 15)
                    .fill(isDisabled ? Color.gray : color)
                    .shadow(color: .black.opacity(0.2), radius: 5, x: 0, y: 3)
            )
        }
        .disabled(isDisabled)
        .scaleEffect(isDisabled ? 0.95 : 1.0)
        .opacity(isDisabled ? 0.6 : 1.0)
    }
}

// MARK: - 底部按钮组件
struct BottomButton: View {
    let icon: String
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.white)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 15)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
        }
    }
}

// MARK: - 毛玻璃效果
struct BlurView: UIViewRepresentable {
    let style: UIBlurEffect.Style
    
    func makeUIView(context: Context) -> UIVisualEffectView {
        UIVisualEffectView(effect: UIBlurEffect(style: style))
    }
    
    func updateUIView(_ uiView: UIVisualEffectView, context: Context) {}
}

// MARK: - 设置界面（简化版）
struct SettingsView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("游戏设置")
                    .font(.title)
                    .fontWeight(.bold)
                    .padding(.top)
                
                Spacer()
                
                VStack(spacing: 15) {
                    SettingRow(title: "音效", value: "开启")
                    SettingRow(title: "音乐", value: "开启")
                    SettingRow(title: "震动反馈", value: "开启")
                    SettingRow(title: "AI难度", value: "普通")
                }
                
                Spacer()
                
                Text("更多设置功能开发中...")
                    .foregroundColor(.gray)
                    .font(.caption)
                
                Spacer()
            }
            .padding()
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct SettingRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.body)
            
            Spacer()
            
            Text(value)
                .foregroundColor(.blue)
                .font(.body)
        }
        .padding(.horizontal)
        .padding(.vertical, 12)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(10)
    }
}

// MARK: - 规则界面（简化版）
struct RulesView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("游戏规则")
                        .font(.title)
                        .fontWeight(.bold)
                        .padding(.top)
                    
                    RuleSection(
                        title: "基本规则",
                        content: """
                        • 三人游戏，每人16张牌
                        • 拥有黑桃3的玩家先出牌
                        • 必须出比上家更大的牌型
                        • 最先出完牌的玩家获胜
                        """
                    )
                    
                    RuleSection(
                        title: "牌型大小",
                        content: """
                        • 单张：3 < 4 < 5 < ... < K < A < 2
                        • 对子：相同点数的两张牌
                        • 三张：相同点数的三张牌
                        • 顺子：五张以上连续的牌
                        • 炸弹：四张相同点数的牌，可打任何牌型
                        """
                    )
                    
                    RuleSection(
                        title: "花色大小",
                        content: """
                        • 黑桃 > 红桃 > 方块 > 梅花
                        • 相同点数时比较花色
                        """
                    )
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct RuleSection: View {
    let title: String
    let content: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(content)
                .font(.body)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
}

// MARK: - 关于界面（简化版）
struct AboutView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                Text("关于跑得快")
                    .font(.title)
                    .fontWeight(.bold)
                    .padding(.top)
                
                VStack(spacing: 15) {
                    Text("版本 1.0.0")
                        .font(.title3)
                        .foregroundColor(.blue)
                    
                    Text("经典扑克牌游戏")
                        .font(.body)
                        .foregroundColor(.secondary)
                    
                    Text("使用 SwiftUI 开发")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                
                Spacer()
                
                Text("© 2025 跑得快游戏")
                    .font(.caption)
                    .foregroundColor(.gray)
                
                Spacer()
            }
            .padding()
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    MainMenuView()
}
