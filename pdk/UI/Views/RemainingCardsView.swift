//
//  RemainingCardsView.swift
//  pdk
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/25.
//

import SwiftUI

/// 剩余牌库视图
struct RemainingCardsView: View {
    @ObservedObject var viewModel: CardRecorderViewModel
    @State private var selectedSuit: CardSuit? = nil
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 统计信息
                StatsHeaderView(viewModel: viewModel)

                // 跑得快牌库说明
                DeckDescriptionView(viewModel: viewModel)

                // 花色筛选
                SuitFilterView(selectedSuit: $selectedSuit)

                // 剩余牌显示
                RemainingCardsGridView(
                    viewModel: viewModel,
                    selectedSuit: selectedSuit
                )

                Spacer()
            }
            .padding()
            .navigationTitle("剩余牌库")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

/// 统计信息头部视图
struct StatsHeaderView: View {
    @ObservedObject var viewModel: CardRecorderViewModel
    
    var body: some View {
        HStack(spacing: 20) {
            StatItemView(
                title: "剩余牌数",
                value: "\(viewModel.getRemainingCardsCount())/48",
                color: .blue
            )

            StatItemView(
                title: "我的手牌",
                value: "\(viewModel.myHandCards.count)",
                color: .green
            )

            StatItemView(
                title: "对手出牌",
                value: "\(viewModel.opponentPlays.flatMap { $0.cards }.count)",
                color: .orange
            )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.1))
        )
    }
}

/// 统计项视图
struct StatItemView: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

/// 跑得快牌库说明视图
struct DeckDescriptionView: View {
    @ObservedObject var viewModel: CardRecorderViewModel

    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "info.circle.fill")
                    .foregroundColor(.blue)
                Text("跑得快牌库组成")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                Spacer()
            }

            Text(viewModel.getDeckDescription())
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color.blue.opacity(0.1))
        )
    }
}

/// 花色筛选视图
struct SuitFilterView: View {
    @Binding var selectedSuit: CardSuit?
    
    var body: some View {
        HStack(spacing: 15) {
            Text("筛选:")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Button("全部") {
                selectedSuit = nil
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(selectedSuit == nil ? Color.blue : Color.gray.opacity(0.2))
            )
            .foregroundColor(selectedSuit == nil ? .white : .primary)
            
            ForEach(CardSuit.allCases, id: \.self) { suit in
                Button(suit.rawValue) {
                    selectedSuit = suit
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(selectedSuit == suit ? Color.blue : Color.gray.opacity(0.2))
                )
                .foregroundColor(selectedSuit == suit ? .white : .primary)
            }
            
            Spacer()
        }
    }
}

/// 剩余牌网格视图
struct RemainingCardsGridView: View {
    @ObservedObject var viewModel: CardRecorderViewModel
    let selectedSuit: CardSuit?
    
    private var filteredCards: [Card] {
        let allCards = CardSuit.allCases.flatMap { suit in
            CardRank.allCases.compactMap { rank in
                let card = Card(suit: suit, rank: rank)
                // 只显示跑得快牌库中的牌
                return viewModel.isCardInDeck(card) ? card : nil
            }
        }

        if let selectedSuit = selectedSuit {
            return allCards.filter { $0.suit == selectedSuit }
        }

        return allCards
    }
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 12) {
                ForEach(filteredCards, id: \.id) { card in
                    RemainingCardItemView(
                        card: card,
                        remainingCount: viewModel.getRemainingCount(for: card),
                        viewModel: viewModel
                    )
                }
            }
            .padding(.horizontal)
        }
    }
}

/// 剩余牌项视图
struct RemainingCardItemView: View {
    let card: Card
    let remainingCount: Int
    let viewModel: CardRecorderViewModel
    
    private var isUsed: Bool {
        remainingCount <= 0
    }
    
    var body: some View {
        VStack(spacing: 8) {
            // 卡牌显示
            ZStack {
                RoundedRectangle(cornerRadius: 8)
                    .fill(isUsed ? Color.gray.opacity(0.3) : Color.white)
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 1, y: 1)
                
                RoundedRectangle(cornerRadius: 8)
                    .stroke(isUsed ? Color.gray.opacity(0.5) : Color.gray.opacity(0.3), lineWidth: 1)
                
                VStack(spacing: 2) {
                    Text(card.rank.displayName)
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(isUsed ? .gray : (card.suit.isRed ? .red : .black))
                    
                    Text(card.suit.rawValue)
                        .font(.system(size: 12))
                        .foregroundColor(isUsed ? .gray : (card.suit.isRed ? .red : .black))
                }
                
                // 已使用标记
                if isUsed {
                    Image(systemName: "xmark.circle.fill")
                        .font(.caption)
                        .foregroundColor(.red)
                        .background(Color.white)
                        .clipShape(Circle())
                        .offset(x: 15, y: -15)
                }

                // 跑得快特殊标记（被移除的牌）
                if !viewModel.isCardInDeck(card) {
                    Image(systemName: "minus.circle.fill")
                        .font(.caption)
                        .foregroundColor(.orange)
                        .background(Color.white)
                        .clipShape(Circle())
                        .offset(x: -15, y: -15)
                }
            }
            .frame(width: 50, height: 70)
            
            // 状态文本
            Text(getCardStatusText())
                .font(.caption2)
                .foregroundColor(getCardStatusColor())
        }
        .opacity(getCardOpacity())
    }

    private func getCardStatusText() -> String {
        if !viewModel.isCardInDeck(card) {
            return "不在库"
        } else if isUsed {
            return "已使用"
        } else {
            return "剩余"
        }
    }

    private func getCardStatusColor() -> Color {
        if !viewModel.isCardInDeck(card) {
            return .orange
        } else if isUsed {
            return .red
        } else {
            return .green
        }
    }

    private func getCardOpacity() -> Double {
        if !viewModel.isCardInDeck(card) {
            return 0.4 // 不在牌库中的牌最透明
        } else if isUsed {
            return 0.6 // 已使用的牌半透明
        } else {
            return 1.0 // 剩余的牌完全不透明
        }
    }
}

#Preview {
    RemainingCardsView(viewModel: CardRecorderViewModel())
}
