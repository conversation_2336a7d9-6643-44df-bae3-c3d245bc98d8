//
//  GameView.swift
//  pdk
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/21.
//

import SwiftUI

/// 游戏主界面
struct GameView: View {
    @ObservedObject var viewModel: GameViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var showExitConfirmation = false
    @State private var showCardRecorder = false
    
    var body: some View {
        ZStack {
            // 背景
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.green.opacity(0.8),
                    Color.green.opacity(0.6)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 顶部状态栏
                TopStatusBar(viewModel: viewModel, showCardRecorder: $showCardRecorder) {
                    showExitConfirmation = true
                }
                
                // 游戏区域
                GameAreaView(viewModel: viewModel)
                
                // 底部控制区
                BottomControlView(viewModel: viewModel)
            }
        }
        .navigationBarHidden(true)
        .alert("退出游戏", isPresented: $showExitConfirmation) {
            But<PERSON>("取消", role: .cancel) { }
            But<PERSON>("退出", role: .destructive) {
                dismiss()
            }
        } message: {
            Text("确定要退出当前游戏吗？")
        }
        .sheet(isPresented: $viewModel.showGameResult) {
            GameResultView(viewModel: viewModel)
        }
        .overlay {
            if viewModel.isLoading {
                LoadingView()
            }
        }
        .fullScreenCover(isPresented: $showCardRecorder) {
            CardRecorderView()
        }
    }
}

// MARK: - 顶部状态栏
struct TopStatusBar: View {
    @ObservedObject var viewModel: GameViewModel
    @Binding var showCardRecorder: Bool
    let onExit: () -> Void
    
    var body: some View {
        HStack {
            // 退出按钮
            Button(action: onExit) {
                Image(systemName: "xmark.circle.fill")
                    .font(.title2)
                    .foregroundColor(.white)
                    .background(Color.black.opacity(0.3))
                    .clipShape(Circle())
            }
            
            Spacer()
            
            // 游戏信息
            VStack(spacing: 4) {
                Text("第 \(viewModel.gameState.roundNumber) 回合")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
                
                Text(viewModel.getGameTimeDescription())
                    .font(.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
            }
            
            Spacer()

            // 记牌器按钮
            Button(action: { showCardRecorder = true }) {
                Image(systemName: "camera.viewfinder")
                    .font(.title2)
                    .foregroundColor(.white)
                    .background(Color.black.opacity(0.3))
                    .clipShape(Circle())
            }

            // 设置按钮
            Button(action: { viewModel.showSettingsView() }) {
                Image(systemName: "gearshape.fill")
                    .font(.title2)
                    .foregroundColor(.white)
                    .background(Color.black.opacity(0.3))
                    .clipShape(Circle())
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 10)
    }
}

// MARK: - 游戏区域
struct GameAreaView: View {
    @ObservedObject var viewModel: GameViewModel
    
    var body: some View {
        VStack(spacing: 20) {
            // AI玩家2（上方）
            if let topPlayer = viewModel.gameState.players.first(where: { $0.position == 2 }) {
                PlayerView(
                    player: topPlayer,
                    isCurrentPlayer: viewModel.currentPlayer?.id == topPlayer.id,
                    position: .top
                )
            }
            
            HStack {
                // AI玩家1（左侧）
                if let leftPlayer = viewModel.gameState.players.first(where: { $0.position == 1 }) {
                    PlayerView(
                        player: leftPlayer,
                        isCurrentPlayer: viewModel.currentPlayer?.id == leftPlayer.id,
                        position: .left
                    )
                }
                
                Spacer()
                
                // 中央出牌区域
                CenterPlayAreaView(viewModel: viewModel)
                
                Spacer()
                
                // 占位（保持布局平衡）
                Color.clear
                    .frame(width: 80)
            }
            
            // 人类玩家（下方）
            if let humanPlayer = viewModel.humanPlayer {
                HumanPlayerView(
                    player: humanPlayer,
                    viewModel: viewModel,
                    isCurrentPlayer: viewModel.currentPlayer?.id == humanPlayer.id
                )
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 10)
    }
}

// MARK: - 玩家视图
struct PlayerView: View {
    let player: Player
    let isCurrentPlayer: Bool
    let position: PlayerPosition
    
    var body: some View {
        VStack(spacing: 8) {
            // 玩家信息
            HStack(spacing: 10) {
                // 头像
                Circle()
                    .fill(Color.blue.opacity(0.7))
                    .frame(width: 40, height: 40)
                    .overlay {
                        Text(String(player.name.prefix(1)))
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(player.name)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                    
                    Text("\(player.cardCount)张牌")
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.8))
                }
                
                if isCurrentPlayer {
                    Image(systemName: "clock.fill")
                        .font(.caption)
                        .foregroundColor(.yellow)
                }
            }
            
            // 手牌（背面）
            if position == .top {
                HStack(spacing: -10) {
                    ForEach(0..<min(player.cardCount, 8), id: \.self) { _ in
                        CardBackView()
                            .frame(width: 30, height: 42)
                    }
                    
                    if player.cardCount > 8 {
                        Text("+\(player.cardCount - 8)")
                            .font(.caption2)
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.black.opacity(0.5))
                            .cornerRadius(8)
                    }
                }
            } else {
                VStack(spacing: -15) {
                    ForEach(0..<min(player.cardCount, 6), id: \.self) { _ in
                        CardBackView()
                            .frame(width: 30, height: 42)
                    }
                    
                    if player.cardCount > 6 {
                        Text("+\(player.cardCount - 6)")
                            .font(.caption2)
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.black.opacity(0.5))
                            .cornerRadius(6)
                    }
                }
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isCurrentPlayer ? Color.yellow.opacity(0.3) : Color.black.opacity(0.2))
                .stroke(isCurrentPlayer ? Color.yellow : Color.clear, lineWidth: 2)
        )
    }
}

// MARK: - 人类玩家视图
struct HumanPlayerView: View {
    let player: Player
    @ObservedObject var viewModel: GameViewModel
    let isCurrentPlayer: Bool

    var body: some View {
        ZStack(alignment: .topLeading) {
            VStack(spacing: 15) {
                // 顶部空间，为左上角信息预留位置
                HStack {
                    Spacer()

                    if isCurrentPlayer {
                        HStack(spacing: 5) {
                            Image(systemName: "clock.fill")
                                .foregroundColor(.yellow)

                            Text(viewModel.getRemainingTimeDescription())
                                .font(.title3)
                                .fontWeight(.bold)
                                .foregroundColor(.yellow)
                        }
                    }
                }
                .frame(height: 30) // 为左上角信息预留高度

                // 手牌 - 重叠显示
                OverlappingCardsView(
                    cards: player.cards,
                    selectedCards: viewModel.selectedCards,
                    onCardTap: { card in
                        viewModel.toggleCardSelection(card)
                    },
                    onCardDoubleTap: { card in
                        viewModel.doubleTapToPlay(card)
                    },
                    onSwipeSelection: { cards in
                        // 批量切换选中状态
                        for card in cards {
                            viewModel.toggleCardSelection(card)
                        }
                    }
                )
            }
            .padding(.vertical, 15)
            .background(
                RoundedRectangle(cornerRadius: 15)
                    .fill(Color.black.opacity(0.3))
                    .stroke(isCurrentPlayer ? Color.yellow : Color.clear, lineWidth: 2)
            )

            // 左上角牌数信息 - 绝对定位覆盖层
            VStack(alignment: .leading, spacing: 2) {
                Text("手牌 \(player.cardCount)张")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.black.opacity(0.7))
                    )

                Text("左右滑动快速选牌")
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.8))
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(Color.black.opacity(0.5))
                    )
            }
            .padding(.leading, 20)
            .padding(.top, 20)
        }
    }
}

// MARK: - 中央出牌区域
struct CenterPlayAreaView: View {
    @ObservedObject var viewModel: GameViewModel
    
    var body: some View {
        VStack(spacing: 15) {
            // 当前牌型显示
            if let combination = viewModel.gameState.currentCombination {
                VStack(spacing: 8) {
                    Text(combination.type.rawValue)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.white.opacity(0.8))
                    
                    HStack(spacing: 5) {
                        ForEach(combination.cards, id: \.id) { card in
                            CardView(card: card, isSelected: false, onTap: {}, onDoubleTap: nil)
                                .frame(width: 40, height: 56)
                        }
                    }
                }
                .padding(15)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white.opacity(0.2))
                )
            } else {
                VStack(spacing: 10) {
                    Image(systemName: "suit.club.fill")
                        .font(.system(size: 40))
                        .foregroundColor(.white.opacity(0.5))
                    
                    Text("自由出牌")
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(.white.opacity(0.8))
                }
                .padding(20)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white.opacity(0.1))
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
            }
            
            // 游戏提示
            if let hint = viewModel.getPlayHints().first {
                Text(hint)
                    .font(.caption)
                    .foregroundColor(.yellow)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        Capsule()
                            .fill(Color.black.opacity(0.5))
                    )
            }
        }
    }
}

// MARK: - 底部控制区
struct BottomControlView: View {
    @ObservedObject var viewModel: GameViewModel
    
    var body: some View {
        HStack(spacing: 20) {
            // 过牌按钮
            Button(action: { viewModel.passCards() }) {
                Text("过牌")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(width: 80, height: 44)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(viewModel.canPass ? Color.red : Color.gray)
                    )
            }
            .disabled(!viewModel.canPass)
            
            Spacer()
            
            // 清除选择按钮
            Button(action: { viewModel.clearSelection() }) {
                Text("清除")
                    .font(.subheadline)
                    .foregroundColor(.white)
                    .frame(width: 60, height: 36)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(Color.gray.opacity(0.8))
                    )
            }
            .disabled(viewModel.selectedCards.isEmpty)
            
            // 出牌按钮
            Button(action: { viewModel.playSelectedCards() }) {
                Text("出牌")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(width: 80, height: 44)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(viewModel.canPlaySelectedCards ? Color.green : Color.gray)
                    )
            }
            .disabled(!viewModel.canPlaySelectedCards)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 15)
        .background(
            Rectangle()
                .fill(Color.black.opacity(0.3))
        )
    }
}

// MARK: - 重叠卡牌视图
struct OverlappingCardsView: View {
    let cards: [Card]
    let selectedCards: Set<Card>
    let onCardTap: (Card) -> Void
    let onCardDoubleTap: (Card) -> Void
    let onSwipeSelection: ([Card]) -> Void

    @State private var dragOffset: CGFloat = 0
    @State private var isDragging = false

    // 计算卡牌重叠间距 - 优化为从左边开始显示
    private var cardSpacing: CGFloat {
        let totalWidth = UIScreen.main.bounds.width - 80 // 减去左右边距，为左上角信息预留更多空间
        let cardWidth: CGFloat = 50
        let minSpacing: CGFloat = 18 // 增加最小间距，确保能看到每张牌
        let maxSpacing: CGFloat = 30 // 增加最大间距

        if cards.isEmpty { return maxSpacing }

        let availableWidth = totalWidth - cardWidth // 保留最后一张完整卡牌的宽度
        let calculatedSpacing = availableWidth / CGFloat(max(1, cards.count - 1))

        return max(minSpacing, min(calculatedSpacing, maxSpacing))
    }

    var body: some View {
        HStack(alignment: .bottom, spacing: 0) {
            // 手牌容器 - 从最左边开始
            ZStack(alignment: .leading) {
                // 滑动选择指示器
                if isDragging && abs(dragOffset) > 20 {
                    Rectangle()
                        .fill(Color.blue.opacity(0.2))
                        .frame(width: abs(dragOffset), height: 90)
                        .offset(x: dragOffset > 0 ? dragOffset / 2 : dragOffset / 2)
                        .animation(.easeOut(duration: 0.1), value: dragOffset)
                }

                ForEach(Array(cards.enumerated()), id: \.element.id) { index, card in
                    CardView(
                        card: card,
                        isSelected: selectedCards.contains(card),
                        onTap: {
                            onCardTap(card)
                        },
                        onDoubleTap: {
                            onCardDoubleTap(card)
                        }
                    )
                    .offset(x: CGFloat(index) * cardSpacing + dragOffset)
                    .zIndex(selectedCards.contains(card) ? Double(cards.count + index) : Double(index)) // 选中的牌在最上层
                    .scaleEffect(selectedCards.contains(card) ? 1.15 : 1.0) // 增加选中缩放效果
                    .offset(y: selectedCards.contains(card) ? -15 : 0) // 增加选中上移距离
                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: selectedCards.contains(card)) // 使用弹簧动画
                    .shadow(color: selectedCards.contains(card) ? .blue.opacity(0.5) : .black.opacity(0.2), radius: selectedCards.contains(card) ? 8 : 3) // 增强阴影效果
                    .overlay(
                        // 选中状态的边框高亮
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(selectedCards.contains(card) ? Color.blue : Color.clear, lineWidth: 2)
                            .animation(.easeInOut(duration: 0.2), value: selectedCards.contains(card))
                    )
                }
            }
            .frame(height: 90) // 增加高度以容纳选中状态的上移
            .clipped()
            .gesture(
                DragGesture()
                    .onChanged { value in
                        if !isDragging {
                            isDragging = true
                        }
                        dragOffset = value.translation.width
                    }
                    .onEnded { value in
                        isDragging = false

                        let velocity = value.predictedEndTranslation.width - value.translation.width
                        let threshold: CGFloat = 50

                        if abs(value.translation.width) > threshold {
                            // 根据滑动方向和距离选择卡牌
                            handleSwipeSelection(translation: value.translation.width, velocity: velocity)
                        }

                        // 重置拖拽偏移
                        withAnimation(.easeOut(duration: 0.3)) {
                            dragOffset = 0
                        }
                    }
            )

            Spacer() // 推送手牌到最左边
        }
        .frame(height: 90) // 与上面的高度保持一致
        .padding(.leading, 20) // 只在左边添加边距
    }

    private func handleSwipeSelection(translation: CGFloat, velocity: CGFloat) {
        let swipeDistance = abs(translation)
        let cardCount = max(1, Int(swipeDistance / cardSpacing))

        var cardsToToggle: [Card] = []

        if translation > 0 {
            // 向右滑动，从左边开始选择卡牌
            let endIndex = min(cardCount, cards.count)
            cardsToToggle = Array(cards[0..<endIndex])
        } else {
            // 向左滑动，从右边开始选择卡牌
            let startIndex = max(0, cards.count - cardCount)
            cardsToToggle = Array(cards[startIndex..<cards.count])
        }

        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        onSwipeSelection(cardsToToggle)
    }
}

// MARK: - 辅助枚举
enum PlayerPosition {
    case top, left, bottom
}

#Preview {
    GameView(viewModel: GameViewModel())
}
