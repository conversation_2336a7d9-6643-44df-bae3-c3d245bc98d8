//
//  CardRecorderComponents.swift
//  pdk
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/22.
//

import SwiftUI

// MARK: - 状态指示器
struct StatusIndicatorView: View {
    let title: String
    let status: TaskStatus
    let description: String
    
    var body: some View {
        HStack(spacing: 15) {
            // 状态图标
            ZStack {
                Circle()
                    .fill(status.backgroundColor)
                    .frame(width: 40, height: 40)
                
                Image(systemName: status.iconName)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(status.iconColor)
            }
            
            // 状态信息
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
        .padding(.horizontal)
    }
}

enum TaskStatus {
    case pending
    case inProgress
    case completed
    
    var backgroundColor: Color {
        switch self {
        case .pending: return Color.gray.opacity(0.2)
        case .inProgress: return Color.blue.opacity(0.2)
        case .completed: return Color.green.opacity(0.2)
        }
    }
    
    var iconColor: Color {
        switch self {
        case .pending: return .gray
        case .inProgress: return .blue
        case .completed: return .green
        }
    }
    
    var iconName: String {
        switch self {
        case .pending: return "clock"
        case .inProgress: return "arrow.clockwise"
        case .completed: return "checkmark"
        }
    }
}

// MARK: - 对手出牌页面
struct OpponentCardsTab: View {
    @ObservedObject var viewModel: CardRecorderViewModel
    @State private var showAddOpponentPlay = false
    
    var body: some View {
        VStack(spacing: 20) {
            // 状态指示器
            StatusIndicatorView(
                title: "第二步：记录对手出牌",
                status: viewModel.opponentPlays.isEmpty ? .pending : .inProgress,
                description: "已记录 \(viewModel.opponentPlays.count) 次出牌"
            )
            
            // 添加出牌按钮
            Button(action: { showAddOpponentPlay = true }) {
                HStack {
                    Image(systemName: "plus.circle.fill")
                        .font(.title2)
                    Text("记录对手出牌")
                        .font(.headline)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(Color.blue)
                .cornerRadius(12)
            }
            .padding(.horizontal)
            
            // 出牌记录列表
            if viewModel.opponentPlays.isEmpty {
                VStack(spacing: 15) {
                    Image(systemName: "person.2.badge.plus")
                        .font(.system(size: 50))
                        .foregroundColor(.gray)
                    
                    Text("还没有对手出牌记录")
                        .font(.headline)
                        .foregroundColor(.gray)
                    
                    Text("点击上方按钮开始记录")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                List {
                    ForEach(Array(viewModel.opponentPlays.enumerated()), id: \.element.id) { index, play in
                        OpponentPlayRowView(play: play) {
                            viewModel.removeOpponentPlay(at: index)
                        }
                    }
                }
                .listStyle(PlainListStyle())
            }
            
            Spacer()
        }
        .padding(.top)
        .sheet(isPresented: $showAddOpponentPlay) {
            AddOpponentPlayView(viewModel: viewModel)
        }
    }
}

// MARK: - 对手出牌行视图
struct OpponentPlayRowView: View {
    let play: OpponentPlay
    let onDelete: () -> Void
    
    var body: some View {
        HStack(spacing: 15) {
            // 玩家标识
            VStack {
                Image(systemName: play.player == .leftPlayer ? "arrow.up.circle.fill" : "arrow.down.circle.fill")
                    .font(.title2)
                    .foregroundColor(play.player == .leftPlayer ? .blue : .green)
                
                Text(play.player.rawValue)
                    .font(.caption)
                    .fontWeight(.semibold)
            }
            .frame(width: 50)
            
            // 出牌内容
            VStack(alignment: .leading, spacing: 8) {
                Text(play.combination.type.rawValue)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                // 卡牌显示
                HStack(spacing: 4) {
                    ForEach(play.cards.prefix(5), id: \.id) { card in
                        CardView(card: card, isSelected: false, onTap: {})
                            .frame(width: 30, height: 42)
                    }
                    
                    if play.cards.count > 5 {
                        Text("+\(play.cards.count - 5)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Text(DateFormatter.timeFormatter.string(from: play.timestamp))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 删除按钮
            Button(action: onDelete) {
                Image(systemName: "trash")
                    .foregroundColor(.red)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
}

// MARK: - 添加对手出牌视图
struct AddOpponentPlayView: View {
    @ObservedObject var viewModel: CardRecorderViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedPlayer: OpponentPlayer = .leftPlayer
    @State private var selectedCards: [Card] = []
    @State private var showCardPicker = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 选择玩家
                VStack(alignment: .leading, spacing: 10) {
                    Text("选择出牌玩家")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    HStack(spacing: 15) {
                        ForEach(OpponentPlayer.allCases, id: \.self) { player in
                            Button(action: { selectedPlayer = player }) {
                                HStack {
                                    Image(systemName: player == .leftPlayer ? "arrow.up.circle" : "arrow.down.circle")
                                    Text(player.rawValue)
                                }
                                .foregroundColor(selectedPlayer == player ? .white : .blue)
                                .padding()
                                .frame(maxWidth: .infinity)
                                .background(selectedPlayer == player ? Color.blue : Color.blue.opacity(0.1))
                                .cornerRadius(10)
                            }
                        }
                    }
                }
                .padding(.horizontal)
                
                // 选择出牌
                VStack(alignment: .leading, spacing: 10) {
                    HStack {
                        Text("选择出牌内容")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Spacer()
                        
                        Button("选择卡牌") {
                            showCardPicker = true
                        }
                        .foregroundColor(.blue)
                    }
                    
                    if selectedCards.isEmpty {
                        Text("请选择对手出的牌")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity, minHeight: 100)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(10)
                    } else {
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 6), spacing: 8) {
                            ForEach(selectedCards, id: \.id) { card in
                                CardView(card: card, isSelected: false, onTap: {
                                    selectedCards.removeAll { $0.id == card.id }
                                })
                                .frame(width: 45, height: 63)
                            }
                        }
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(10)
                    }
                }
                .padding(.horizontal)
                
                Spacer()
                
                // 确认按钮
                Button("确认添加") {
                    viewModel.addOpponentPlay(player: selectedPlayer, cards: selectedCards)
                    dismiss()
                }
                .disabled(selectedCards.isEmpty)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(selectedCards.isEmpty ? Color.gray : Color.green)
                .cornerRadius(12)
                .padding(.horizontal)
            }
            .padding(.top)
            .navigationTitle("记录出牌")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showCardPicker) {
            CardPickerView(selectedCards: $selectedCards)
        }
    }
}

// MARK: - AI建议页面
struct AIAdviceTab: View {
    @ObservedObject var viewModel: CardRecorderViewModel

    var body: some View {
        VStack(spacing: 20) {
            // 状态指示器
            StatusIndicatorView(
                title: "第三步：获取AI建议",
                status: viewModel.aiAdvice != nil ? .completed : .pending,
                description: viewModel.myHandCards.isEmpty ? "请先识别手牌" : "AI正在分析最佳出牌策略"
            )

            if viewModel.myHandCards.isEmpty {
                // 提示用户先识别手牌
                VStack(spacing: 15) {
                    Image(systemName: "hand.raised.slash")
                        .font(.system(size: 50))
                        .foregroundColor(.gray)

                    Text("请先识别手牌")
                        .font(.headline)
                        .foregroundColor(.gray)

                    Text("在`我的手牌`页面拍照识别您的手牌")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if let advice = viewModel.aiAdvice {
                // 显示AI建议
                ScrollView {
                    VStack(spacing: 20) {
                        // 推荐出牌
                        AIRecommendationCard(advice: advice)

                        // 备选方案
                        if !advice.alternatives.isEmpty {
                            AlternativeOptionsCard(alternatives: advice.alternatives)
                        }

                        // 分析原因
                        AnalysisReasonCard(reasoning: advice.reasoning, confidence: advice.confidence)
                    }
                    .padding(.horizontal)
                }
            } else {
                // 分析中状态
                VStack(spacing: 15) {
                    if viewModel.isAnalyzing {
                        ProgressView()
                            .scaleEffect(1.2)

                        Text("AI正在分析...")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    } else {
                        Button("获取AI建议") {
                            viewModel.requestAIAdvice()
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(Color.green)
                        .cornerRadius(12)
                        .padding(.horizontal)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }

            Spacer()
        }
        .padding(.top)
    }
}

// MARK: - AI推荐卡片
struct AIRecommendationCard: View {
    let advice: AIAdvice

    var body: some View {
        VStack(spacing: 15) {
            HStack {
                Image(systemName: "brain.head.profile")
                    .font(.title2)
                    .foregroundColor(.green)

                Text("AI推荐")
                    .font(.headline)
                    .fontWeight(.bold)

                Spacer()

                Text("\(Int(advice.confidence * 100))%")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.green)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.green.opacity(0.2))
                    .cornerRadius(8)
            }

            if let recommendedPlay = advice.recommendedPlay {
                VStack(spacing: 10) {
                    Text(recommendedPlay.type.rawValue)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: min(6, recommendedPlay.cards.count)), spacing: 8) {
                        ForEach(recommendedPlay.cards, id: \.id) { card in
                            CardView(card: card, isSelected: false, onTap: {})
                                .frame(width: 45, height: 63)
                        }
                    }
                }
            } else {
                VStack(spacing: 10) {
                    Image(systemName: "hand.raised.slash")
                        .font(.system(size: 40))
                        .foregroundColor(.orange)

                    Text("建议过牌")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                }
            }
        }
        .padding()
        .background(Color.green.opacity(0.05))
        .cornerRadius(15)
        .overlay(
            RoundedRectangle(cornerRadius: 15)
                .stroke(Color.green.opacity(0.3), lineWidth: 2)
        )
    }
}

// MARK: - 备选方案卡片
struct AlternativeOptionsCard: View {
    let alternatives: [CardCombination]

    var body: some View {
        VStack(spacing: 15) {
            HStack {
                Image(systemName: "list.bullet")
                    .font(.title2)
                    .foregroundColor(.blue)

                Text("备选方案")
                    .font(.headline)
                    .fontWeight(.bold)

                Spacer()
            }

            ForEach(Array(alternatives.enumerated()), id: \.offset) { index, combination in
                HStack(spacing: 10) {
                    Text("\(index + 1)")
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .frame(width: 24, height: 24)
                        .background(Color.blue)
                        .clipShape(Circle())

                    Text(combination.type.rawValue)
                        .font(.subheadline)
                        .fontWeight(.semibold)

                    Spacer()

                    HStack(spacing: 2) {
                        ForEach(combination.cards.prefix(4), id: \.id) { card in
                            CardView(card: card, isSelected: false, onTap: {})
                                .frame(width: 25, height: 35)
                        }
                        if combination.cards.count > 4 {
                            Text("+\(combination.cards.count - 4)")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding(.vertical, 8)
                .padding(.horizontal, 12)
                .background(Color.blue.opacity(0.05))
                .cornerRadius(10)
            }
        }
        .padding()
        .background(Color.blue.opacity(0.05))
        .cornerRadius(15)
        .overlay(
            RoundedRectangle(cornerRadius: 15)
                .stroke(Color.blue.opacity(0.3), lineWidth: 2)
        )
    }
}

// MARK: - 分析原因卡片
struct AnalysisReasonCard: View {
    let reasoning: String
    let confidence: Double

    var body: some View {
        VStack(spacing: 15) {
            HStack {
                Image(systemName: "lightbulb")
                    .font(.title2)
                    .foregroundColor(.orange)

                Text("分析原因")
                    .font(.headline)
                    .fontWeight(.bold)

                Spacer()
            }

            Text(reasoning)
                .font(.body)
                .foregroundColor(.primary)
                .multilineTextAlignment(.leading)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding()
        .background(Color.orange.opacity(0.05))
        .cornerRadius(15)
        .overlay(
            RoundedRectangle(cornerRadius: 15)
                .stroke(Color.orange.opacity(0.3), lineWidth: 2)
        )
    }
}

// MARK: - 历史记录页面
struct RecordHistoryTab: View {
    @ObservedObject var viewModel: CardRecorderViewModel

    var body: some View {
        VStack {
            Text("历史记录功能开发中...")
                .font(.headline)
                .foregroundColor(.gray)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
    }
}

// MARK: - 卡牌选择器
struct CardPickerView: View {
    @Binding var selectedCards: [Card]
    @Environment(\.dismiss) private var dismiss

    private let allCards: [Card] = {
        var cards: [Card] = []
        for suit in CardSuit.allCases {
            for rank in CardRank.allCases {
                cards.append(Card(suit: suit, rank: rank))
            }
        }
        return cards.sorted { $0.rank.weight < $1.rank.weight }
    }()

    var body: some View {
        NavigationView {
            VStack {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 6), spacing: 8) {
                    ForEach(allCards, id: \.id) { card in
                        CardView(
                            card: card,
                            isSelected: selectedCards.contains(card),
                            onTap: {
                                if selectedCards.contains(card) {
                                    selectedCards.removeAll { $0.id == card.id }
                                } else {
                                    selectedCards.append(card)
                                }
                            }
                        )
                        .frame(width: 45, height: 63)
                    }
                }
                .padding()

                Spacer()
            }
            .navigationTitle("选择卡牌")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - 记牌器使用指南
struct CardRecorderGuideView: View {
    @Binding var isPresented: Bool

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 25) {
                    // 标题
                    VStack(spacing: 10) {
                        Image(systemName: "brain.head.profile")
                            .font(.system(size: 50))
                            .foregroundColor(.blue)

                        Text("智能记牌器使用指南")
                            .font(.title)
                            .fontWeight(.bold)

                        Text("三步轻松获取AI出牌建议")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding(.top)

                    // 步骤说明
                    VStack(spacing: 20) {
                        GuideStepView(
                            stepNumber: 1,
                            title: "识别手牌",
                            description: "拍照识别您的手牌，系统会自动识别所有卡牌",
                            icon: "camera.fill",
                            color: .blue
                        )

                        GuideStepView(
                            stepNumber: 2,
                            title: "记录对手出牌",
                            description: "记录上家和下家的出牌情况，帮助AI分析局势",
                            icon: "person.2.fill",
                            color: .green
                        )

                        GuideStepView(
                            stepNumber: 3,
                            title: "获取AI建议",
                            description: "AI会分析当前局势，给出最佳出牌策略和备选方案",
                            icon: "brain.head.profile",
                            color: .purple
                        )
                    }

                    // 使用技巧
                    VStack(alignment: .leading, spacing: 15) {
                        Text("使用技巧")
                            .font(.headline)
                            .fontWeight(.bold)

                        VStack(spacing: 12) {
                            TipRowView(
                                icon: "lightbulb.fill",
                                tip: "拍照时请确保手牌排列整齐，光线充足"
                            )

                            TipRowView(
                                icon: "clock.fill",
                                tip: "及时记录对手出牌，信息越完整建议越准确"
                            )

                            TipRowView(
                                icon: "star.fill",
                                tip: "AI会根据剩余牌数和对手出牌历史给出建议"
                            )
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(15)

                    Spacer()
                }
                .padding(.horizontal)
            }
            .navigationTitle("使用指南")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        isPresented = false
                    }
                }
            }
        }
    }
}

// MARK: - 指南步骤视图
struct GuideStepView: View {
    let stepNumber: Int
    let title: String
    let description: String
    let icon: String
    let color: Color

    var body: some View {
        HStack(spacing: 15) {
            // 步骤编号
            ZStack {
                Circle()
                    .fill(color)
                    .frame(width: 40, height: 40)

                Text("\(stepNumber)")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }

            // 图标
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 30)

            // 内容
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)

                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
            }

            Spacer()
        }
        .padding()
        .background(color.opacity(0.1))
        .cornerRadius(15)
    }
}

// MARK: - 技巧行视图
struct TipRowView: View {
    let icon: String
    let tip: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.subheadline)
                .foregroundColor(.orange)
                .frame(width: 20)

            Text(tip)
                .font(.subheadline)
                .foregroundColor(.primary)

            Spacer()
        }
    }
}

// MARK: - 扩展
extension DateFormatter {
    static let timeFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        return formatter
    }()
}
