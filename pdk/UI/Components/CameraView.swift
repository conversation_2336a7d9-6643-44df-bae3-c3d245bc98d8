//
//  CameraView.swift
//  pdk
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/21.
//

import SwiftUI
import UIKit
import AVFoundation

/// 相机视图组件 - 用于拍摄扑克牌照片
struct CameraView: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Binding var isPresented: Bool
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .camera
        picker.allowsEditing = true
        picker.cameraDevice = .rear
        
        // 设置相机配置
        if UIImagePickerController.isSourceTypeAvailable(.camera) {
            picker.cameraCaptureMode = .photo
            picker.cameraFlashMode = .auto
        }
        
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSO<PERSON>, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: CameraView
        
        init(_ parent: CameraView) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let editedImage = info[.editedImage] as? UIImage {
                parent.selectedImage = editedImage
            } else if let originalImage = info[.originalImage] as? UIImage {
                parent.selectedImage = originalImage
            }
            
            parent.isPresented = false
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.isPresented = false
        }
    }
}

/// 图片选择器 - 支持相机和相册
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Binding var isPresented: Bool
    let sourceType: UIImagePickerController.SourceType
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = sourceType
        picker.allowsEditing = true
        
        // 如果是相机，设置额外配置
        if sourceType == .camera {
            picker.cameraDevice = .rear
            picker.cameraCaptureMode = .photo
            picker.cameraFlashMode = .auto
        }
        
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker
        
        init(_ parent: ImagePicker) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let editedImage = info[.editedImage] as? UIImage {
                parent.selectedImage = editedImage
            } else if let originalImage = info[.originalImage] as? UIImage {
                parent.selectedImage = originalImage
            }
            
            parent.isPresented = false
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.isPresented = false
        }
    }
}

/// 相机权限管理器
class CameraPermissionManager: ObservableObject {
    @Published var permissionStatus: AVAuthorizationStatus = .notDetermined
    @Published var showPermissionAlert = false
    
    init() {
        checkPermission()
    }
    
    func checkPermission() {
        permissionStatus = AVCaptureDevice.authorizationStatus(for: .video)
    }
    
    func requestPermission() {
        AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
            DispatchQueue.main.async {
                self?.permissionStatus = granted ? .authorized : .denied
                if !granted {
                    self?.showPermissionAlert = true
                }
            }
        }
    }
    
    var canUseCamera: Bool {
        return permissionStatus == .authorized && UIImagePickerController.isSourceTypeAvailable(.camera)
    }
    
    var permissionMessage: String {
        switch permissionStatus {
        case .notDetermined:
            return "需要相机权限来拍摄扑克牌"
        case .denied, .restricted:
            return "相机权限被拒绝，请在设置中开启"
        case .authorized:
            return "相机权限已授权"
        @unknown default:
            return "未知权限状态"
        }
    }
}

/// 拍照按钮组件
struct CameraButton: View {
    let action: () -> Void
    let isEnabled: Bool
    
    var body: some View {
        Button(action: action) {
            ZStack {
                Circle()
                    .fill(isEnabled ? Color.blue : Color.gray)
                    .frame(width: 70, height: 70)
                    .shadow(color: .black.opacity(0.3), radius: 5, x: 0, y: 3)
                
                Circle()
                    .stroke(Color.white, lineWidth: 3)
                    .frame(width: 60, height: 60)
                
                Image(systemName: "camera.fill")
                    .font(.title2)
                    .foregroundColor(.white)
            }
        }
        .disabled(!isEnabled)
        .scaleEffect(isEnabled ? 1.0 : 0.9)
        .opacity(isEnabled ? 1.0 : 0.6)
    }
}

/// 图片源选择器
struct ImageSourcePicker: View {
    @Binding var showCamera: Bool
    @Binding var showPhotoLibrary: Bool
    @Binding var isPresented: Bool
    @StateObject private var permissionManager = CameraPermissionManager()
    
    var body: some View {
        VStack(spacing: 20) {
            Text("选择图片来源")
                .font(.headline)
                .padding(.top)
            
            VStack(spacing: 15) {
                // 拍照选项
                Button(action: {
                    if permissionManager.canUseCamera {
                        showCamera = true
                        isPresented = false
                    } else {
                        permissionManager.requestPermission()
                    }
                }) {
                    HStack {
                        Image(systemName: "camera.fill")
                            .font(.title3)
                            .foregroundColor(.blue)
                        
                        Text("拍照")
                            .font(.body)
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        if !permissionManager.canUseCamera {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .font(.caption)
                                .foregroundColor(.orange)
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(10)
                }
                .disabled(!permissionManager.canUseCamera)
                
                // 相册选项
                Button(action: {
                    showPhotoLibrary = true
                    isPresented = false
                }) {
                    HStack {
                        Image(systemName: "photo.fill")
                            .font(.title3)
                            .foregroundColor(.green)
                        
                        Text("从相册选择")
                            .font(.body)
                            .foregroundColor(.primary)
                        
                        Spacer()
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(10)
                }
            }
            
            // 权限提示
            if !permissionManager.canUseCamera {
                Text(permissionManager.permissionMessage)
                    .font(.caption)
                    .foregroundColor(.orange)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            
            // 取消按钮
            Button("取消") {
                isPresented = false
            }
            .foregroundColor(.red)
            .padding(.bottom)
        }
        .padding()
        .alert("相机权限", isPresented: $permissionManager.showPermissionAlert) {
            Button("设置") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            Button("取消", role: .cancel) {}
        } message: {
            Text("请在设置中开启相机权限以使用拍照功能")
        }
    }
}

/// 图片预览组件
struct ImagePreviewView: View {
    let image: UIImage
    let onConfirm: () -> Void
    let onRetake: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            // 图片预览
            Image(uiImage: image)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(maxHeight: 400)
                .cornerRadius(12)
                .shadow(color: .black.opacity(0.2), radius: 5)
            
            // 提示文字
            Text("请确认照片中的扑克牌清晰可见")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            // 按钮组
            HStack(spacing: 30) {
                // 重新拍摄
                Button(action: onRetake) {
                    VStack(spacing: 5) {
                        Image(systemName: "camera.rotate.fill")
                            .font(.title2)
                        Text("重拍")
                            .font(.caption)
                    }
                    .foregroundColor(.orange)
                    .padding()
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(10)
                }
                
                // 确认使用
                Button(action: onConfirm) {
                    VStack(spacing: 5) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.title2)
                        Text("确认")
                            .font(.caption)
                    }
                    .foregroundColor(.green)
                    .padding()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(10)
                }
            }
        }
        .padding()
    }
}

/// 拍照指导界面
struct CameraGuideView: View {
    @Binding var isPresented: Bool
    
    var body: some View {
        VStack(spacing: 25) {
            // 标题
            Text("拍照指南")
                .font(.title2)
                .fontWeight(.bold)
                .padding(.top)
            
            // 指导步骤
            VStack(alignment: .leading, spacing: 15) {
                GuideStep(
                    number: 1,
                    title: "摆放扑克牌",
                    description: "将扑克牌平铺在光线充足的平面上"
                )
                
                GuideStep(
                    number: 2,
                    title: "保持距离",
                    description: "手机距离扑克牌约20-30厘米"
                )
                
                GuideStep(
                    number: 3,
                    title: "对准拍摄",
                    description: "确保所有扑克牌都在取景框内且清晰可见"
                )
                
                GuideStep(
                    number: 4,
                    title: "避免反光",
                    description: "避免强光直射造成反光影响识别"
                )
            }
            
            Spacer()
            
            // 开始拍照按钮
            Button(action: {
                isPresented = false
            }) {
                Text("开始拍照")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(12)
            }
            .padding(.horizontal)
            .padding(.bottom)
        }
        .padding()
    }
}

/// 指导步骤组件
struct GuideStep: View {
    let number: Int
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 15) {
            // 步骤编号
            ZStack {
                Circle()
                    .fill(Color.blue)
                    .frame(width: 30, height: 30)
                
                Text("\(number)")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            
            // 步骤内容
            VStack(alignment: .leading, spacing: 5) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.body)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

#Preview("相机按钮") {
    VStack(spacing: 30) {
        CameraButton(action: {}, isEnabled: true)
        CameraButton(action: {}, isEnabled: false)
    }
    .padding()
}

#Preview("图片源选择器") {
    ImageSourcePicker(
        showCamera: .constant(false),
        showPhotoLibrary: .constant(false),
        isPresented: .constant(true)
    )
}

#Preview("拍照指南") {
    CameraGuideView(isPresented: .constant(true))
}
