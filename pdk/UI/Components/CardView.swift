//
//  CardView.swift
//  pdk
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/21.
//

import SwiftUI

/// 扑克牌视图组件
struct CardView: View {
    let card: Card
    let isSelected: Bool
    let onTap: () -> Void
    let onDoubleTap: (() -> Void)?

    @State private var isPressed = false

    // 初始化方法，onDoubleTap为可选参数
    init(card: Card, isSelected: Bool, onTap: @escaping () -> Void, onDoubleTap: (() -> Void)? = nil) {
        self.card = card
        self.isSelected = isSelected
        self.onTap = onTap
        self.onDoubleTap = onDoubleTap
    }
    
    var body: some View {
        Button(action: onTap) {
            ZStack {
                // 卡牌背景
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.white)
                    .shadow(color: .black.opacity(0.2), radius: 3, x: 1, y: 2)

                // 卡牌边框
                RoundedRectangle(cornerRadius: 8)
                    .stroke(isSelected ? Color.blue : Color.gray.opacity(0.3), lineWidth: isSelected ? 3 : 1)

                // 卡牌内容
                VStack(spacing: 2) {
                    // 点数
                    Text(card.rank.displayName)
                        .font(.system(size: 16, weight: .bold, design: .rounded))
                        .foregroundColor(card.suit.isRed ? .red : .black)

                    // 花色
                    Text(card.suit.rawValue)
                        .font(.system(size: 14))
                        .foregroundColor(card.suit.isRed ? .red : .black)
                }
            }
        }
        .frame(width: 50, height: 70)
        .scaleEffect(isSelected ? 1.1 : (isPressed ? 0.95 : 1.0))
        .offset(y: isSelected ? -10 : 0)
        .animation(.easeInOut(duration: 0.2), value: isSelected)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .simultaneousGesture(
            // 双击手势 - 如果卡牌已选中且提供了双击回调，则执行双击出牌
            TapGesture(count: 2)
                .onEnded { _ in
                    if isSelected, let doubleTapAction = onDoubleTap {
                        doubleTapAction()
                    }
                }
        )
    }
}

/// 扑克牌背面视图
struct CardBackView: View {
    var body: some View {
        ZStack {
            // 背景
            RoundedRectangle(cornerRadius: 8)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.blue.opacity(0.8),
                            Color.blue.opacity(0.6)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(color: .black.opacity(0.2), radius: 3, x: 1, y: 2)
            
            // 边框
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color.white.opacity(0.3), lineWidth: 1)
            
            // 图案
            VStack(spacing: 4) {
                Image(systemName: "suit.club.fill")
                    .font(.system(size: 12))
                    .foregroundColor(.white.opacity(0.8))
                
                Image(systemName: "suit.diamond.fill")
                    .font(.system(size: 8))
                    .foregroundColor(.white.opacity(0.6))
                
                Image(systemName: "suit.heart.fill")
                    .font(.system(size: 12))
                    .foregroundColor(.white.opacity(0.8))
                
                Image(systemName: "suit.spade.fill")
                    .font(.system(size: 8))
                    .foregroundColor(.white.opacity(0.6))
            }
        }
    }
}

/// 大尺寸卡牌视图（用于展示）
struct LargeCardView: View {
    let card: Card
    
    var body: some View {
        ZStack {
            // 背景
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white)
                .shadow(color: .black.opacity(0.3), radius: 5, x: 2, y: 3)
            
            // 边框
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray.opacity(0.3), lineWidth: 2)
            
            VStack(spacing: 8) {
                // 左上角标识
                HStack {
                    VStack(spacing: 2) {
                        Text(card.rank.displayName)
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(card.suit.isRed ? .red : .black)
                        
                        Text(card.suit.rawValue)
                            .font(.system(size: 16))
                            .foregroundColor(card.suit.isRed ? .red : .black)
                    }
                    
                    Spacer()
                }
                .padding(.top, 8)
                .padding(.leading, 8)
                
                Spacer()
                
                // 中央花色
                Text(card.suit.rawValue)
                    .font(.system(size: 40))
                    .foregroundColor(card.suit.isRed ? .red : .black)
                
                Spacer()
                
                // 右下角标识（倒置）
                HStack {
                    Spacer()
                    
                    VStack(spacing: 2) {
                        Text(card.suit.rawValue)
                            .font(.system(size: 16))
                            .foregroundColor(card.suit.isRed ? .red : .black)
                            .rotationEffect(.degrees(180))
                        
                        Text(card.rank.displayName)
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(card.suit.isRed ? .red : .black)
                            .rotationEffect(.degrees(180))
                    }
                }
                .padding(.bottom, 8)
                .padding(.trailing, 8)
            }
        }
        .frame(width: 80, height: 112)
    }
}

/// 卡牌组合视图
struct CardCombinationView: View {
    let combination: CardCombination
    let spacing: CGFloat = 5
    
    var body: some View {
        VStack(spacing: 8) {
            // 牌型标题
            Text(combination.type.rawValue)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 4)
                .background(
                    Capsule()
                        .fill(Color.black.opacity(0.6))
                )
            
            // 卡牌
            HStack(spacing: spacing) {
                ForEach(combination.cards, id: \.id) { card in
                    CardView(card: card, isSelected: false, onTap: {}, onDoubleTap: nil)
                        .frame(width: 40, height: 56)
                }
            }
        }
    }
}

/// 动画卡牌视图
struct AnimatedCardView: View {
    let card: Card
    let isSelected: Bool
    let onTap: () -> Void
    
    @State private var animationOffset: CGFloat = 0
    @State private var animationScale: CGFloat = 1.0
    
    var body: some View {
        CardView(card: card, isSelected: isSelected, onTap: onTap, onDoubleTap: nil)
            .offset(y: animationOffset)
            .scaleEffect(animationScale)
            .onAppear {
                // 发牌动画
                withAnimation(.easeOut(duration: 0.5)) {
                    animationOffset = 0
                    animationScale = 1.0
                }
            }
    }
}

/// 卡牌选择器视图
struct CardSelectorView: View {
    let cards: [Card]
    @Binding var selectedCards: Set<Card>
    let onSelectionChange: () -> Void
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(cards, id: \.id) { card in
                    CardView(
                        card: card,
                        isSelected: selectedCards.contains(card),
                        onTap: {
                            if selectedCards.contains(card) {
                                selectedCards.remove(card)
                            } else {
                                selectedCards.insert(card)
                            }
                            onSelectionChange()
                        },
                        onDoubleTap: nil
                    )
                }
            }
            .padding(.horizontal, 20)
        }
    }
}

// MARK: - 预览
#Preview("单张卡牌") {
    VStack(spacing: 20) {
        CardView(
            card: Card(suit: .spades, rank: .ace),
            isSelected: false,
            onTap: {},
            onDoubleTap: nil
        )

        CardView(
            card: Card(suit: .hearts, rank: .king),
            isSelected: true,
            onTap: {},
            onDoubleTap: { print("双击出牌") }
        )

        CardBackView()
            .frame(width: 50, height: 70)
    }
    .padding()
    .background(Color.green.opacity(0.3))
}

#Preview("大尺寸卡牌") {
    HStack(spacing: 20) {
        LargeCardView(card: Card(suit: .spades, rank: .three))
        LargeCardView(card: Card(suit: .hearts, rank: .two))
    }
    .padding()
    .background(Color.green.opacity(0.3))
}

#Preview("卡牌组合") {
    let cards = [
        Card(suit: .spades, rank: .three),
        Card(suit: .hearts, rank: .three),
        Card(suit: .diamonds, rank: .three)
    ]
    let combination = CardCombination(cards: cards)
    
    CardCombinationView(combination: combination)
        .padding()
        .background(Color.green.opacity(0.3))
}
