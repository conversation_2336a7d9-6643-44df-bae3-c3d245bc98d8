//
//  GameState.swift
//  pdk
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/21.
//

import Foundation

/// 游戏阶段枚举
enum GamePhase: String, Codable {
    case waiting = "waiting"         // 等待玩家
    case ready = "ready"            // 准备阶段
    case dealing = "dealing"        // 发牌阶段
    case playing = "playing"        // 游戏进行中
    case finished = "finished"      // 游戏结束
    case paused = "paused"          // 游戏暂停
}

/// 游戏模式枚举
enum GameMode: String, Codable {
    case single = "single"          // 单机模式
    case online = "online"          // 联机模式
    case tournament = "tournament"  // 锦标赛模式
}

/// 游戏配置结构体
struct GameConfig: Codable {
    var playerCount: Int = 3        // 玩家数量
    var cardsPerPlayer: Int = 16    // 每人手牌数
    var enableBomb: Bool = true     // 是否启用炸弹
    var enableStraight: Bool = true // 是否启用顺子
    var timeLimit: Int = 30         // 出牌时间限制（秒）
    var baseScore: Int = 100        // 基础分数
    var bombMultiplier: Int = 2     // 炸弹倍数
    
    /// 默认配置
    static let `default` = GameConfig()
    
    /// 快速游戏配置
    static let quick = GameConfig(
        playerCount: 3,
        cardsPerPlayer: 16,
        timeLimit: 15
    )
    
    /// 锦标赛配置
    static let tournament = GameConfig(
        playerCount: 3,
        cardsPerPlayer: 16,
        timeLimit: 20,
        baseScore: 200
    )
}

/// 游戏回合信息
struct GameRound: Codable {
    let roundNumber: Int
    let playerId: UUID
    let combination: CardCombination?
    let timestamp: Date
    let isPass: Bool
    
    init(roundNumber: Int, playerId: UUID, combination: CardCombination? = nil, isPass: Bool = false) {
        self.roundNumber = roundNumber
        self.playerId = playerId
        self.combination = combination
        self.timestamp = Date()
        self.isPass = isPass
    }
}

/// 游戏结果
struct GameResult: Codable {
    let gameId: UUID
    let winners: [UUID]             // 获胜者ID列表（按名次排序）
    let scores: [UUID: Int]         // 玩家得分
    let duration: TimeInterval      // 游戏时长
    let totalRounds: Int            // 总回合数
    let finishTime: Date
    
    init(gameId: UUID, winners: [UUID], scores: [UUID: Int], duration: TimeInterval, totalRounds: Int) {
        self.gameId = gameId
        self.winners = winners
        self.scores = scores
        self.duration = duration
        self.totalRounds = totalRounds
        self.finishTime = Date()
    }
}

/// 游戏状态主类
class GameState: ObservableObject, Codable {
    // MARK: - 基本属性
    let gameId: UUID
    var mode: GameMode
    var config: GameConfig
    var phase: GamePhase
    var players: [Player]
    
    // MARK: - 游戏进行状态
    var currentPlayerIndex: Int
    var currentCombination: CardCombination?
    var lastPlayerId: UUID?
    var roundNumber: Int
    var passCount: Int              // 连续过牌次数
    var gameHistory: [GameRound]    // 游戏历史记录
    
    // MARK: - 时间相关
    var startTime: Date?
    var currentTurnStartTime: Date?
    var remainingTime: Int          // 当前玩家剩余时间
    
    // MARK: - 游戏结果
    var result: GameResult?
    var isGameOver: Bool
    
    // MARK: - 编码键
    enum CodingKeys: String, CodingKey {
        case gameId, mode, config, phase, players
        case currentPlayerIndex, currentCombination, lastPlayerId
        case roundNumber, passCount, gameHistory
        case startTime, currentTurnStartTime, remainingTime
        case result, isGameOver
    }
    
    // MARK: - 初始化
    init(mode: GameMode = .single, config: GameConfig = .default) {
        self.gameId = UUID()
        self.mode = mode
        self.config = config
        self.phase = .waiting
        self.players = []
        self.currentPlayerIndex = 0
        self.currentCombination = nil
        self.lastPlayerId = nil
        self.roundNumber = 0
        self.passCount = 0
        self.gameHistory = []
        self.startTime = nil
        self.currentTurnStartTime = nil
        self.remainingTime = config.timeLimit
        self.result = nil
        self.isGameOver = false
    }
    
    // MARK: - 编码/解码
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        gameId = try container.decode(UUID.self, forKey: .gameId)
        mode = try container.decode(GameMode.self, forKey: .mode)
        config = try container.decode(GameConfig.self, forKey: .config)
        phase = try container.decode(GamePhase.self, forKey: .phase)
        players = try container.decode([Player].self, forKey: .players)
        currentPlayerIndex = try container.decode(Int.self, forKey: .currentPlayerIndex)
        currentCombination = try container.decodeIfPresent(CardCombination.self, forKey: .currentCombination)
        lastPlayerId = try container.decodeIfPresent(UUID.self, forKey: .lastPlayerId)
        roundNumber = try container.decode(Int.self, forKey: .roundNumber)
        passCount = try container.decode(Int.self, forKey: .passCount)
        gameHistory = try container.decode([GameRound].self, forKey: .gameHistory)
        startTime = try container.decodeIfPresent(Date.self, forKey: .startTime)
        currentTurnStartTime = try container.decodeIfPresent(Date.self, forKey: .currentTurnStartTime)
        remainingTime = try container.decode(Int.self, forKey: .remainingTime)
        result = try container.decodeIfPresent(GameResult.self, forKey: .result)
        isGameOver = try container.decode(Bool.self, forKey: .isGameOver)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        try container.encode(gameId, forKey: .gameId)
        try container.encode(mode, forKey: .mode)
        try container.encode(config, forKey: .config)
        try container.encode(phase, forKey: .phase)
        try container.encode(players, forKey: .players)
        try container.encode(currentPlayerIndex, forKey: .currentPlayerIndex)
        try container.encodeIfPresent(currentCombination, forKey: .currentCombination)
        try container.encodeIfPresent(lastPlayerId, forKey: .lastPlayerId)
        try container.encode(roundNumber, forKey: .roundNumber)
        try container.encode(passCount, forKey: .passCount)
        try container.encode(gameHistory, forKey: .gameHistory)
        try container.encodeIfPresent(startTime, forKey: .startTime)
        try container.encodeIfPresent(currentTurnStartTime, forKey: .currentTurnStartTime)
        try container.encode(remainingTime, forKey: .remainingTime)
        try container.encodeIfPresent(result, forKey: .result)
        try container.encode(isGameOver, forKey: .isGameOver)
    }
    
    // MARK: - 游戏控制方法
    
    /// 添加玩家
    func addPlayer(_ player: Player) {
        guard players.count < config.playerCount else { return }
        players.append(player)
        
        if players.count == config.playerCount {
            phase = .ready
        }
    }
    
    /// 移除玩家
    func removePlayer(withId id: UUID) {
        players.removeAll { $0.id == id }
        if players.count < config.playerCount {
            phase = .waiting
        }
    }
    
    /// 获取当前玩家
    var currentPlayer: Player? {
        guard currentPlayerIndex < players.count else { return nil }
        return players[currentPlayerIndex]
    }
    
    /// 获取下一个玩家索引
    func nextPlayerIndex() -> Int {
        return (currentPlayerIndex + 1) % players.count
    }
    
    /// 切换到下一个玩家
    func nextTurn() {
        currentPlayerIndex = nextPlayerIndex()
        currentTurnStartTime = Date()
        remainingTime = config.timeLimit
        roundNumber += 1
    }
    
    /// 开始游戏
    func startGame() {
        guard phase == .ready && players.count == config.playerCount else { return }
        
        phase = .dealing
        startTime = Date()
        dealCards()
        
        // 找到有黑桃3的玩家先出牌
        if let spadeThreePlayerIndex = players.firstIndex(where: { $0.hasSpadeThree }) {
            currentPlayerIndex = spadeThreePlayerIndex
        }
        
        phase = .playing
        currentTurnStartTime = Date()
        remainingTime = config.timeLimit
    }
    
    /// 发牌
    private func dealCards() {
        let deck = Card.shuffleDeck(Card.createDeck())
        let cardsPerPlayer = config.cardsPerPlayer
        
        for i in 0..<players.count {
            let startIndex = i * cardsPerPlayer
            let endIndex = min(startIndex + cardsPerPlayer, deck.count)
            let playerCards = Array(deck[startIndex..<endIndex])
            players[i].addCards(playerCards)
        }
    }
    
    /// 玩家出牌
    func playCards(_ combination: CardCombination, playerId: UUID) -> Bool {
        guard let playerIndex = players.firstIndex(where: { $0.id == playerId }),
              phase == .playing,
              currentPlayer?.id == playerId else {
            return false
        }
        
        // 验证出牌是否有效
        if !players[playerIndex].canPlay(combination) {
            return false
        }
        
        // 验证是否可以打过当前牌型
        if let current = currentCombination {
            if !CardCombination.canBeat(combination, current) {
                return false
            }
        }
        
        // 执行出牌
        players[playerIndex].removeCards(combination.cards)
        players[playerIndex].updateLastPlayTime()
        
        currentCombination = combination
        lastPlayerId = playerId
        passCount = 0
        
        // 记录游戏历史
        let round = GameRound(roundNumber: roundNumber, playerId: playerId, combination: combination)
        gameHistory.append(round)
        
        // 检查游戏是否结束
        if players[playerIndex].hasFinished {
            endGame()
            return true
        }
        
        nextTurn()
        return true
    }
    
    /// 玩家过牌
    func passCards(playerId: UUID) -> Bool {
        guard let playerIndex = players.firstIndex(where: { $0.id == playerId }),
              phase == .playing,
              currentPlayer?.id == playerId else {
            return false
        }
        
        passCount += 1
        
        // 记录过牌历史
        let round = GameRound(roundNumber: roundNumber, playerId: playerId, isPass: true)
        gameHistory.append(round)
        
        // 如果所有其他玩家都过牌，清空当前牌型
        if passCount >= players.count - 1 {
            currentCombination = nil
            passCount = 0
        }
        
        nextTurn()
        return true
    }
    
    /// 结束游戏
    private func endGame() {
        phase = .finished
        isGameOver = true
        
        // 计算游戏结果
        let finishedPlayers = players.filter { $0.hasFinished }
        let winners = finishedPlayers.map { $0.id }
        
        var scores: [UUID: Int] = [:]
        for player in players {
            let remainingCards = player.cardCount
            scores[player.id] = max(0, config.baseScore - remainingCards * 10)
        }
        
        let duration = Date().timeIntervalSince(startTime ?? Date())
        result = GameResult(
            gameId: gameId,
            winners: winners,
            scores: scores,
            duration: duration,
            totalRounds: roundNumber
        )
    }
    
    /// 重置游戏
    func resetGame() {
        phase = .waiting
        players.removeAll()
        currentPlayerIndex = 0
        currentCombination = nil
        lastPlayerId = nil
        roundNumber = 0
        passCount = 0
        gameHistory.removeAll()
        startTime = nil
        currentTurnStartTime = nil
        remainingTime = config.timeLimit
        result = nil
        isGameOver = false
    }
}
