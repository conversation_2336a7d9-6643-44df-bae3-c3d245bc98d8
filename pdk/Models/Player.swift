//
//  Player.swift
//  pdk
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/21.
//

import Foundation

/// 玩家类型枚举
enum PlayerType: String, Codable {
    case human = "human"      // 真人玩家
    case ai = "ai"           // AI玩家
    case remote = "remote"   // 远程玩家（联机模式）
}

/// 玩家状态枚举
enum PlayerStatus: String, Codable {
    case waiting = "waiting"     // 等待中
    case playing = "playing"     // 游戏中
    case finished = "finished"   // 已完成
    case offline = "offline"     // 离线
}

/// 玩家结构体
struct Player: Identifiable, Codable, Equatable {
    let id: UUID
    var name: String
    var type: PlayerType
    var status: PlayerStatus
    var cards: [Card]           // 手牌
    var position: Int           // 座位位置 (0, 1, 2)
    var coins: Int              // 金币数量
    var avatar: String          // 头像标识
    var isReady: Bool           // 是否准备
    var lastPlayTime: Date?     // 最后出牌时间
    
    /// 初始化玩家
    init(id: UUID = UUID(), 
         name: String, 
         type: PlayerType, 
         position: Int,
         coins: Int = 1000,
         avatar: String = "default") {
        self.id = id
        self.name = name
        self.type = type
        self.status = .waiting
        self.cards = []
        self.position = position
        self.coins = coins
        self.avatar = avatar
        self.isReady = false
        self.lastPlayTime = nil
    }
    
    /// 手牌数量
    var cardCount: Int {
        return cards.count
    }
    
    /// 是否已出完牌
    var hasFinished: Bool {
        return cards.isEmpty
    }
    
    /// 是否有黑桃3
    var hasSpadeThree: Bool {
        return cards.contains { $0.isSpadeThree }
    }
    
    /// 添加手牌
    mutating func addCards(_ newCards: [Card]) {
        cards.append(contentsOf: newCards)
        sortCards()
    }
    
    /// 移除手牌
    mutating func removeCards(_ cardsToRemove: [Card]) {
        for card in cardsToRemove {
            if let index = cards.firstIndex(of: card) {
                cards.remove(at: index)
            }
        }
    }
    
    /// 清空手牌
    mutating func clearCards() {
        cards.removeAll()
    }
    
    /// 排序手牌（按权重排序）
    mutating func sortCards() {
        cards.sort { $0.weight < $1.weight }
    }
    
    /// 设置准备状态
    mutating func setReady(_ ready: Bool) {
        isReady = ready
    }
    
    /// 设置玩家状态
    mutating func setStatus(_ newStatus: PlayerStatus) {
        status = newStatus
    }
    
    /// 更新最后出牌时间
    mutating func updateLastPlayTime() {
        lastPlayTime = Date()
    }
    
    /// 增加金币
    mutating func addCoins(_ amount: Int) {
        coins += amount
    }
    
    /// 减少金币
    mutating func deductCoins(_ amount: Int) -> Bool {
        if coins >= amount {
            coins -= amount
            return true
        }
        return false
    }
    
    /// 获取可出的牌型组合
    func getPossibleCombinations() -> [CardCombination] {
        var combinations: [CardCombination] = []
        
        // 单张
        for card in cards {
            combinations.append(CardCombination(cards: [card]))
        }
        
        // 对子
        let pairs = findPairs()
        combinations.append(contentsOf: pairs)
        
        // 三张
        let triples = findTriples()
        combinations.append(contentsOf: triples)
        
        // 炸弹
        let bombs = findBombs()
        combinations.append(contentsOf: bombs)
        
        // 顺子
        let straights = findStraights()
        combinations.append(contentsOf: straights)
        
        // 连对
        let pairStraights = findPairStraights()
        combinations.append(contentsOf: pairStraights)
        
        // 飞机
        let tripleStraights = findTripleStraights()
        combinations.append(contentsOf: tripleStraights)
        
        return combinations.filter { $0.isValid }
    }
    
    /// 查找对子
    private func findPairs() -> [CardCombination] {
        var pairs: [CardCombination] = []
        let rankGroups = Dictionary(grouping: cards, by: { $0.rank })
        
        for (_, cards) in rankGroups {
            if cards.count >= 2 {
                // 可以组成对子
                for i in 0..<cards.count-1 {
                    for j in i+1..<cards.count {
                        pairs.append(CardCombination(cards: [cards[i], cards[j]]))
                    }
                }
            }
        }
        
        return pairs
    }
    
    /// 查找三张
    private func findTriples() -> [CardCombination] {
        var triples: [CardCombination] = []
        let rankGroups = Dictionary(grouping: cards, by: { $0.rank })
        
        for (_, cards) in rankGroups {
            if cards.count >= 3 {
                // 可以组成三张
                for i in 0..<cards.count-2 {
                    for j in i+1..<cards.count-1 {
                        for k in j+1..<cards.count {
                            triples.append(CardCombination(cards: [cards[i], cards[j], cards[k]]))
                        }
                    }
                }
            }
        }
        
        return triples
    }
    
    /// 查找炸弹
    private func findBombs() -> [CardCombination] {
        var bombs: [CardCombination] = []
        let rankGroups = Dictionary(grouping: cards, by: { $0.rank })
        
        for (_, cards) in rankGroups {
            if cards.count == 4 {
                bombs.append(CardCombination(cards: cards))
            }
        }
        
        return bombs
    }
    
    /// 查找顺子（简化实现）
    private func findStraights() -> [CardCombination] {
        // 简化实现，后续可以优化
        return []
    }
    
    /// 查找连对（简化实现）
    private func findPairStraights() -> [CardCombination] {
        // 简化实现，后续可以优化
        return []
    }
    
    /// 查找飞机（简化实现）
    private func findTripleStraights() -> [CardCombination] {
        // 简化实现，后续可以优化
        return []
    }
    
    /// 检查是否可以出指定牌型
    func canPlay(_ combination: CardCombination) -> Bool {
        // 检查是否拥有这些牌
        for card in combination.cards {
            if !cards.contains(card) {
                return false
            }
        }
        return combination.isValid
    }
}

// MARK: - AI玩家扩展
extension Player {
    /// AI玩家决策（简单贪心策略）
    func makeAIDecision(currentCombination: CardCombination?) -> CardCombination? {
        guard type == .ai else { return nil }
        
        let possibleCombinations = getPossibleCombinations()
        
        // 如果没有当前牌型，出最小的单张
        guard let current = currentCombination else {
            // 如果有黑桃3，必须先出
            if hasSpadeThree {
                let spadeThree = cards.first { $0.isSpadeThree }!
                return CardCombination(cards: [spadeThree])
            }
            
            // 出最小的单张
            if let smallestCard = cards.first {
                return CardCombination(cards: [smallestCard])
            }
            return nil
        }
        
        // 找到可以打过当前牌型的最小组合
        let validCombinations = possibleCombinations.filter { 
            CardCombination.canBeat($0, current) 
        }
        
        if let bestCombination = validCombinations.min(by: { 
            $0.mainRank?.weight ?? 0 < $1.mainRank?.weight ?? 0 
        }) {
            return bestCombination
        }
        
        return nil // 选择不出牌
    }
}
