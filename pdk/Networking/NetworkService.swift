//
//  NetworkService.swift
//  pdk
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/21.
//

import Foundation
import Network
import Combine

/// 网络服务 - 处理联机游戏通信（预留接口）
class NetworkService: ObservableObject {
    
    // MARK: - 单例
    static let shared = NetworkService()
    
    // MARK: - 发布属性
    @Published var connectionStatus: ConnectionStatus = .disconnected
    @Published var connectedPlayers: [RemotePlayer] = []
    @Published var roomInfo: RoomInfo?
    @Published var isHost: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - 私有属性
    private var webSocketTask: URLSessionWebSocketTask?
    private var urlSession: URLSession?
    private var networkMonitor: NWPathMonitor?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 配置
    private let serverURL = "wss://your-game-server.com/ws"  // 替换为实际服务器地址
    private let apiBaseURL = "https://your-api-server.com/api"  // 替换为实际API地址
    
    // MARK: - 初始化
    private init() {
        setupNetworkMonitoring()
    }
    
    // MARK: - 网络监控
    
    private func setupNetworkMonitoring() {
        networkMonitor = NWPathMonitor()
        networkMonitor?.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                if path.status == .satisfied {
                    self?.connectionStatus = .connected
                } else {
                    self?.connectionStatus = .disconnected
                    self?.handleDisconnection()
                }
            }
        }
        
        let queue = DispatchQueue(label: "NetworkMonitor")
        networkMonitor?.start(queue: queue)
    }
    
    // MARK: - 连接管理
    
    /// 连接到游戏服务器
    func connectToServer() {
        guard connectionStatus != .connecting else { return }
        
        connectionStatus = .connecting
        
        guard let url = URL(string: serverURL) else {
            setError("无效的服务器地址")
            connectionStatus = .disconnected
            return
        }
        
        urlSession = URLSession(configuration: .default)
        webSocketTask = urlSession?.webSocketTask(with: url)
        
        webSocketTask?.resume()
        startListening()
        
        // 模拟连接延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
            self?.connectionStatus = .connected
        }
    }
    
    /// 断开连接
    func disconnect() {
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil
        urlSession = nil
        connectionStatus = .disconnected
        connectedPlayers.removeAll()
        roomInfo = nil
        isHost = false
    }
    
    /// 处理断开连接
    private func handleDisconnection() {
        disconnect()
        setError("网络连接已断开")
    }
    
    // MARK: - 消息处理
    
    private func startListening() {
        webSocketTask?.receive { [weak self] result in
            switch result {
            case .success(let message):
                self?.handleMessage(message)
                self?.startListening() // 继续监听
                
            case .failure(let error):
                self?.handleError(error)
            }
        }
    }
    
    private func handleMessage(_ message: URLSessionWebSocketTask.Message) {
        switch message {
        case .string(let text):
            handleTextMessage(text)
        case .data(let data):
            handleDataMessage(data)
        @unknown default:
            break
        }
    }
    
    private func handleTextMessage(_ text: String) {
        guard let data = text.data(using: .utf8),
              let message = try? JSONDecoder().decode(NetworkMessage.self, from: data) else {
            return
        }
        
        DispatchQueue.main.async { [weak self] in
            self?.processNetworkMessage(message)
        }
    }
    
    private func handleDataMessage(_ data: Data) {
        // 处理二进制消息（如果需要）
    }
    
    private func processNetworkMessage(_ message: NetworkMessage) {
        switch message.type {
        case .createRoom:
            handleCreateRoom(message)
        case .joinRoom:
            handleJoinRoom(message)
        case .leaveRoom:
            handleLeaveRoom(message)
        case .playerJoined:
            handlePlayerJoined(message)
        case .playerLeft:
            handlePlayerLeft(message)
        case .gameStart:
            handleGameStart(message)
        case .gameMove:
            handleGameMove(message)
        case .gameEnd:
            handleGameEnd(message)
        case .roomUpdate:
            handleRoomUpdate(message)
        case .error:
            handleServerError(message)
        }
    }
    
    // MARK: - 房间管理
    
    /// 创建房间
    func createRoom(roomName: String, maxPlayers: Int = 3) {
        let message = NetworkMessage(
            type: .createRoom,
            data: [
                "roomName": roomName,
                "maxPlayers": maxPlayers
            ]
        )
        
        sendMessage(message)
        isHost = true
    }
    
    /// 加入房间
    func joinRoom(roomId: String) {
        let message = NetworkMessage(
            type: .joinRoom,
            data: ["roomId": roomId]
        )
        
        sendMessage(message)
        isHost = false
    }
    
    /// 离开房间
    func leaveRoom() {
        let message = NetworkMessage(
            type: .leaveRoom,
            data: [:]
        )
        
        sendMessage(message)
        
        roomInfo = nil
        connectedPlayers.removeAll()
        isHost = false
    }
    
    /// 获取房间列表
    func getRoomList(completion: @escaping ([RoomInfo]) -> Void) {
        // 模拟API调用
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let mockRooms = [
                RoomInfo(id: "room1", name: "快速游戏", currentPlayers: 2, maxPlayers: 3, isPrivate: false),
                RoomInfo(id: "room2", name: "高手对决", currentPlayers: 1, maxPlayers: 3, isPrivate: false),
                RoomInfo(id: "room3", name: "朋友局", currentPlayers: 3, maxPlayers: 3, isPrivate: true)
            ]
            completion(mockRooms)
        }
    }
    
    // MARK: - 游戏通信
    
    /// 发送游戏动作
    func sendGameAction(_ action: GameAction) {
        let message = NetworkMessage(
            type: .gameMove,
            data: action.toDictionary()
        )
        
        sendMessage(message)
    }
    
    /// 开始游戏
    func startGame() {
        guard isHost else {
            setError("只有房主可以开始游戏")
            return
        }
        
        let message = NetworkMessage(
            type: .gameStart,
            data: [:]
        )
        
        sendMessage(message)
    }
    
    // MARK: - 消息发送
    
    private func sendMessage(_ message: NetworkMessage) {
        guard let data = try? JSONEncoder().encode(message),
              let text = String(data: data, encoding: .utf8) else {
            setError("消息编码失败")
            return
        }
        
        webSocketTask?.send(.string(text)) { [weak self] error in
            if let error = error {
                self?.handleError(error)
            }
        }
    }
    
    // MARK: - 消息处理器

    private func handleCreateRoom(_ message: NetworkMessage) {
        // 处理创建房间响应
        if let roomData = message.data["room"] as? [String: Any] {
            roomInfo = RoomInfo.fromDictionary(roomData)
        }
    }

    private func handleJoinRoom(_ message: NetworkMessage) {
        // 处理加入房间响应
        if let roomData = message.data["room"] as? [String: Any] {
            roomInfo = RoomInfo.fromDictionary(roomData)
        }
    }

    private func handleLeaveRoom(_ message: NetworkMessage) {
        // 处理离开房间响应
        roomInfo = nil
        connectedPlayers.removeAll()
    }

    private func handlePlayerJoined(_ message: NetworkMessage) {
        // 处理玩家加入
        if let playerData = message.data["player"] as? [String: Any],
           let player = RemotePlayer.fromDictionary(playerData) {
            connectedPlayers.append(player)
        }
    }
    
    private func handlePlayerLeft(_ message: NetworkMessage) {
        // 处理玩家离开
        if let playerId = message.data["playerId"] as? String {
            connectedPlayers.removeAll { $0.id == playerId }
        }
    }
    
    private func handleGameStart(_ message: NetworkMessage) {
        // 处理游戏开始
        // 这里可以通知游戏引擎开始联机游戏
    }
    
    private func handleGameMove(_ message: NetworkMessage) {
        // 处理游戏动作
        // 这里可以将远程玩家的动作传递给游戏引擎
    }
    
    private func handleGameEnd(_ message: NetworkMessage) {
        // 处理游戏结束
    }
    
    private func handleRoomUpdate(_ message: NetworkMessage) {
        // 处理房间信息更新
        if let roomData = message.data["room"] as? [String: Any] {
            roomInfo = RoomInfo.fromDictionary(roomData)
        }
    }
    
    private func handleServerError(_ message: NetworkMessage) {
        if let errorMsg = message.data["message"] as? String {
            setError(errorMsg)
        }
    }
    
    // MARK: - 错误处理
    
    private func handleError(_ error: Error) {
        DispatchQueue.main.async { [weak self] in
            self?.setError("网络错误: \(error.localizedDescription)")
            self?.connectionStatus = .disconnected
        }
    }
    
    private func setError(_ message: String) {
        errorMessage = message
        
        // 5秒后清除错误信息
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) { [weak self] in
            if self?.errorMessage == message {
                self?.errorMessage = nil
            }
        }
    }
}

// MARK: - 数据模型

/// 连接状态
enum ConnectionStatus {
    case disconnected
    case connecting
    case connected
}

/// 远程玩家
struct RemotePlayer: Identifiable, Codable {
    let id: String
    let name: String
    let avatar: String
    var isReady: Bool
    
    static func fromDictionary(_ dict: [String: Any]) -> RemotePlayer? {
        guard let id = dict["id"] as? String,
              let name = dict["name"] as? String,
              let avatar = dict["avatar"] as? String,
              let isReady = dict["isReady"] as? Bool else {
            return nil
        }
        
        return RemotePlayer(id: id, name: name, avatar: avatar, isReady: isReady)
    }
}

/// 房间信息
struct RoomInfo: Identifiable, Codable {
    let id: String
    let name: String
    let currentPlayers: Int
    let maxPlayers: Int
    let isPrivate: Bool
    
    var isFull: Bool {
        return currentPlayers >= maxPlayers
    }
    
    static func fromDictionary(_ dict: [String: Any]) -> RoomInfo? {
        guard let id = dict["id"] as? String,
              let name = dict["name"] as? String,
              let currentPlayers = dict["currentPlayers"] as? Int,
              let maxPlayers = dict["maxPlayers"] as? Int,
              let isPrivate = dict["isPrivate"] as? Bool else {
            return nil
        }
        
        return RoomInfo(id: id, name: name, currentPlayers: currentPlayers, maxPlayers: maxPlayers, isPrivate: isPrivate)
    }
}

/// 网络消息
struct NetworkMessage: Codable {
    let type: MessageType
    let data: [String: Any]
    let timestamp: Date
    
    init(type: MessageType, data: [String: Any]) {
        self.type = type
        self.data = data
        self.timestamp = Date()
    }
    
    enum CodingKeys: String, CodingKey {
        case type, data, timestamp
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        type = try container.decode(MessageType.self, forKey: .type)
        timestamp = try container.decode(Date.self, forKey: .timestamp)
        
        // 简化处理，实际应用中需要更复杂的解码逻辑
        data = [:]
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(type, forKey: .type)
        try container.encode(timestamp, forKey: .timestamp)
        // 简化处理，实际应用中需要更复杂的编码逻辑
    }
}

/// 消息类型
enum MessageType: String, Codable {
    case createRoom
    case joinRoom
    case leaveRoom
    case playerJoined
    case playerLeft
    case gameStart
    case gameMove
    case gameEnd
    case roomUpdate
    case error
}

/// 游戏动作
struct GameAction {
    let type: ActionType
    let playerId: String
    let data: [String: Any]
    
    enum ActionType: String {
        case playCards
        case pass
        case ready
    }
    
    func toDictionary() -> [String: Any] {
        return [
            "type": type.rawValue,
            "playerId": playerId,
            "data": data
        ]
    }
}
